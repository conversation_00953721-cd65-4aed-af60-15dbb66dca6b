{"cells": [{"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n"]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [], "source": ["df=pd.read_csv(\"../datas/heart_attack_prediction_dataset.csv\")"]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Patient ID</th>\n", "      <th>Age</th>\n", "      <th>Sex</th>\n", "      <th>Cholesterol</th>\n", "      <th>Blood Pressure</th>\n", "      <th>Heart Rate</th>\n", "      <th>Diabetes</th>\n", "      <th>Family History</th>\n", "      <th>Smoking</th>\n", "      <th>Obesity</th>\n", "      <th>Alcohol Consumption</th>\n", "      <th>Exercise Hours Per Week</th>\n", "      <th>Diet</th>\n", "      <th>Previous Heart Problems</th>\n", "      <th>Medication Use</th>\n", "      <th>Stress Level</th>\n", "      <th>Sedentary Hours Per Day</th>\n", "      <th>Income</th>\n", "      <th>BMI</th>\n", "      <th>Triglycerides</th>\n", "      <th>Physical Activity Days Per Week</th>\n", "      <th>Sleep Hours Per Day</th>\n", "      <th>Country</th>\n", "      <th>Continent</th>\n", "      <th>Hemisphere</th>\n", "      <th>Heart Attack Risk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>BMW7812</td>\n", "      <td>67</td>\n", "      <td>Male</td>\n", "      <td>208</td>\n", "      <td>158/88</td>\n", "      <td>72</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4.168189</td>\n", "      <td>Average</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>6.615001</td>\n", "      <td>261404</td>\n", "      <td>31.251233</td>\n", "      <td>286</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>Argentina</td>\n", "      <td>South America</td>\n", "      <td>Southern Hemisphere</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CZE1114</td>\n", "      <td>21</td>\n", "      <td>Male</td>\n", "      <td>389</td>\n", "      <td>165/93</td>\n", "      <td>98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.813242</td>\n", "      <td>Unhealthy</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>4.963459</td>\n", "      <td>285768</td>\n", "      <td>27.194973</td>\n", "      <td>235</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>Canada</td>\n", "      <td>North America</td>\n", "      <td>Northern Hemisphere</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>BNI9906</td>\n", "      <td>21</td>\n", "      <td>Female</td>\n", "      <td>324</td>\n", "      <td>174/99</td>\n", "      <td>72</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2.078353</td>\n", "      <td>Healthy</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>9.463426</td>\n", "      <td>235282</td>\n", "      <td>28.176571</td>\n", "      <td>587</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>France</td>\n", "      <td>Europe</td>\n", "      <td>Northern Hemisphere</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>JLN3497</td>\n", "      <td>84</td>\n", "      <td>Male</td>\n", "      <td>383</td>\n", "      <td>163/100</td>\n", "      <td>73</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>9.828130</td>\n", "      <td>Average</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>7.648981</td>\n", "      <td>125640</td>\n", "      <td>36.464704</td>\n", "      <td>378</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>Canada</td>\n", "      <td>North America</td>\n", "      <td>Northern Hemisphere</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>GFO8847</td>\n", "      <td>66</td>\n", "      <td>Male</td>\n", "      <td>318</td>\n", "      <td>91/88</td>\n", "      <td>93</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>5.804299</td>\n", "      <td>Unhealthy</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>1.514821</td>\n", "      <td>160555</td>\n", "      <td>21.809144</td>\n", "      <td>231</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>Thailand</td>\n", "      <td>Asia</td>\n", "      <td>Northern Hemisphere</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Patient ID  Age  ...           Hemisphere  Heart Attack Risk\n", "0    BMW7812   67  ...  Southern Hemisphere                  0\n", "1    CZE1114   21  ...  Northern Hemisphere                  0\n", "2    BNI9906   21  ...  Northern Hemisphere                  0\n", "3    JLN3497   84  ...  Northern Hemisphere                  0\n", "4    GFO8847   66  ...  Northern Hemisphere                  0\n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 145, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [{"data": {"text/plain": ["0       BMW7812\n", "1       CZE1114\n", "2       BNI9906\n", "3       JLN3497\n", "4       GFO8847\n", "         ...   \n", "8758    MSV9918\n", "8759    QSV6764\n", "8760    XKA5925\n", "8761    EPE6801\n", "8762    ZWN9666\n", "Name: Patient ID, Length: 8763, dtype: object"]}, "execution_count": 146, "metadata": {}, "output_type": "execute_result"}], "source": ["df.pop('Patient ID')"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Sex</th>\n", "      <th>Cholesterol</th>\n", "      <th>Blood Pressure</th>\n", "      <th>Heart Rate</th>\n", "      <th>Diabetes</th>\n", "      <th>Family History</th>\n", "      <th>Smoking</th>\n", "      <th>Obesity</th>\n", "      <th>Alcohol Consumption</th>\n", "      <th>Exercise Hours Per Week</th>\n", "      <th>Diet</th>\n", "      <th>Previous Heart Problems</th>\n", "      <th>Medication Use</th>\n", "      <th>Stress Level</th>\n", "      <th>Sedentary Hours Per Day</th>\n", "      <th>Income</th>\n", "      <th>BMI</th>\n", "      <th>Triglycerides</th>\n", "      <th>Physical Activity Days Per Week</th>\n", "      <th>Sleep Hours Per Day</th>\n", "      <th>Country</th>\n", "      <th>Continent</th>\n", "      <th>Hemisphere</th>\n", "      <th>Heart Attack Risk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67</td>\n", "      <td>Male</td>\n", "      <td>208</td>\n", "      <td>158/88</td>\n", "      <td>72</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4.168189</td>\n", "      <td>Average</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>6.615001</td>\n", "      <td>261404</td>\n", "      <td>31.251233</td>\n", "      <td>286</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>Argentina</td>\n", "      <td>South America</td>\n", "      <td>Southern Hemisphere</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21</td>\n", "      <td>Male</td>\n", "      <td>389</td>\n", "      <td>165/93</td>\n", "      <td>98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.813242</td>\n", "      <td>Unhealthy</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>4.963459</td>\n", "      <td>285768</td>\n", "      <td>27.194973</td>\n", "      <td>235</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>Canada</td>\n", "      <td>North America</td>\n", "      <td>Northern Hemisphere</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21</td>\n", "      <td>Female</td>\n", "      <td>324</td>\n", "      <td>174/99</td>\n", "      <td>72</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2.078353</td>\n", "      <td>Healthy</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>9.463426</td>\n", "      <td>235282</td>\n", "      <td>28.176571</td>\n", "      <td>587</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>France</td>\n", "      <td>Europe</td>\n", "      <td>Northern Hemisphere</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>84</td>\n", "      <td>Male</td>\n", "      <td>383</td>\n", "      <td>163/100</td>\n", "      <td>73</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>9.828130</td>\n", "      <td>Average</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>7.648981</td>\n", "      <td>125640</td>\n", "      <td>36.464704</td>\n", "      <td>378</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>Canada</td>\n", "      <td>North America</td>\n", "      <td>Northern Hemisphere</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>66</td>\n", "      <td>Male</td>\n", "      <td>318</td>\n", "      <td>91/88</td>\n", "      <td>93</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>5.804299</td>\n", "      <td>Unhealthy</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>1.514821</td>\n", "      <td>160555</td>\n", "      <td>21.809144</td>\n", "      <td>231</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>Thailand</td>\n", "      <td>Asia</td>\n", "      <td>Northern Hemisphere</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Age     Sex  ...           Hemisphere Heart Attack Risk\n", "0   67    Male  ...  Southern Hemisphere                 0\n", "1   21    Male  ...  Northern Hemisphere                 0\n", "2   21  Female  ...  Northern Hemisphere                 0\n", "3   84    Male  ...  Northern Hemisphere                 0\n", "4   66    Male  ...  Northern Hemisphere                 0\n", "\n", "[5 rows x 25 columns]"]}, "execution_count": 147, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Age', 'Sex', 'Cholesterol', 'Blood Pressure', 'Heart Rate', 'Diabetes',\n", "       'Family History', 'Smoking', 'Obesity', 'Alcohol Consumption',\n", "       'Exercise Hours Per Week', 'Diet', 'Previous Heart Problems',\n", "       'Medication Use', 'Stress Level', 'Sedentary Hours Per Day', 'Income',\n", "       'BMI', 'Triglycerides', 'Physical Activity Days Per Week',\n", "       'Sleep Hours Per Day', 'Country', 'Continent', 'Hemisphere',\n", "       'Heart Attack Risk'],\n", "      dtype='object')"]}, "execution_count": 148, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 149, "metadata": {}, "outputs": [], "source": ["df.drop(columns=df.columns[[21,22,23]],axis=1,inplace=True)"]}, {"cell_type": "code", "execution_count": 150, "metadata": {}, "outputs": [{"data": {"text/plain": ["(8763, 22)"]}, "execution_count": 150, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 151, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 8763 entries, 0 to 8762\n", "Data columns (total 22 columns):\n", " #   Column                           Non-Null Count  Dtype  \n", "---  ------                           --------------  -----  \n", " 0   Age                              8763 non-null   int64  \n", " 1   Sex                              8763 non-null   object \n", " 2   Cholesterol                      8763 non-null   int64  \n", " 3   Blood Pressure                   8763 non-null   object \n", " 4   Heart Rate                       8763 non-null   int64  \n", " 5   Diabetes                         8763 non-null   int64  \n", " 6   Family History                   8763 non-null   int64  \n", " 7   Smoking                          8763 non-null   int64  \n", " 8   Obesity                          8763 non-null   int64  \n", " 9   Alcohol Consumption              8763 non-null   int64  \n", " 10  Exercise Hours Per Week          8763 non-null   float64\n", " 11  Diet                             8763 non-null   object \n", " 12  Previous Heart Problems          8763 non-null   int64  \n", " 13  Medication Use                   8763 non-null   int64  \n", " 14  Stress Level                     8763 non-null   int64  \n", " 15  Sedentary Hours Per Day          8763 non-null   float64\n", " 16  Income                           8763 non-null   int64  \n", " 17  BMI                              8763 non-null   float64\n", " 18  Triglycerides                    8763 non-null   int64  \n", " 19  Physical Activity Days Per Week  8763 non-null   int64  \n", " 20  Sleep Hours Per Day              8763 non-null   int64  \n", " 21  Heart Attack Risk                8763 non-null   int64  \n", "dtypes: float64(3), int64(16), object(3)\n", "memory usage: 1.5+ MB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 152, "metadata": {}, "outputs": [{"data": {"text/plain": ["0         Average\n", "1       Unhealthy\n", "2         Healthy\n", "3         Average\n", "4       Unhealthy\n", "          ...    \n", "8758      Healthy\n", "8759      Healthy\n", "8760      Average\n", "8761    Unhealthy\n", "8762      Healthy\n", "Name: Diet, Length: 8763, dtype: object"]}, "execution_count": 152, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Diet']"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import LabelEncoder\n", "LE=LabelEncoder()"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [], "source": ["Sex=df['Sex']\n", "Diet=df['Diet']"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [], "source": ["Sex=LE.fit_transform(Sex)\n", "Diet=LE.fit_transform(Diet)"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 1, 0, ..., 1, 1, 0])"]}, "execution_count": 156, "metadata": {}, "output_type": "execute_result"}], "source": ["Sex"]}, {"cell_type": "code", "execution_count": 157, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 2, 1, ..., 0, 2, 1])"]}, "execution_count": 157, "metadata": {}, "output_type": "execute_result"}], "source": ["Diet"]}, {"cell_type": "code", "execution_count": 158, "metadata": {}, "outputs": [], "source": ["df.drop(columns=['Sex','Diet'],axis=1,inplace=True)"]}, {"cell_type": "code", "execution_count": 159, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Age', 'Cholesterol', 'Blood Pressure', 'Heart Rate', 'Diabetes',\n", "       'Family History', 'Smoking', 'Obesity', 'Alcohol Consumption',\n", "       'Exercise Hours Per Week', 'Previous Heart Problems', 'Medication Use',\n", "       'Stress Level', 'Sedentary Hours Per Day', 'Income', 'BMI',\n", "       'Triglycerides', 'Physical Activity Days Per Week',\n", "       'Sleep Hours Per Day', 'Heart Attack Risk'],\n", "      dtype='object')"]}, "execution_count": 159, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 8763 entries, 0 to 8762\n", "Data columns (total 20 columns):\n", " #   Column                           Non-Null Count  Dtype  \n", "---  ------                           --------------  -----  \n", " 0   Age                              8763 non-null   int64  \n", " 1   Cholesterol                      8763 non-null   int64  \n", " 2   Blood Pressure                   8763 non-null   object \n", " 3   Heart Rate                       8763 non-null   int64  \n", " 4   Diabetes                         8763 non-null   int64  \n", " 5   Family History                   8763 non-null   int64  \n", " 6   Smoking                          8763 non-null   int64  \n", " 7   Obesity                          8763 non-null   int64  \n", " 8   Alcohol Consumption              8763 non-null   int64  \n", " 9   Exercise Hours Per Week          8763 non-null   float64\n", " 10  Previous Heart Problems          8763 non-null   int64  \n", " 11  Medication Use                   8763 non-null   int64  \n", " 12  Stress Level                     8763 non-null   int64  \n", " 13  Sedentary Hours Per Day          8763 non-null   float64\n", " 14  Income                           8763 non-null   int64  \n", " 15  BMI                              8763 non-null   float64\n", " 16  Triglycerides                    8763 non-null   int64  \n", " 17  Physical Activity Days Per Week  8763 non-null   int64  \n", " 18  Sleep Hours Per Day              8763 non-null   int64  \n", " 19  Heart Attack Risk                8763 non-null   int64  \n", "dtypes: float64(3), int64(16), object(1)\n", "memory usage: 1.3+ MB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [], "source": ["df['Sex']=Sex\n", "df['Diet']=Diet"]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Age', 'Cholesterol', 'Blood Pressure', 'Heart Rate', 'Diabetes',\n", "       'Family History', 'Smoking', 'Obesity', 'Alcohol Consumption',\n", "       'Exercise Hours Per Week', 'Previous Heart Problems', 'Medication Use',\n", "       'Stress Level', 'Sedentary Hours Per Day', 'Income', 'BMI',\n", "       'Triglycerides', 'Physical Activity Days Per Week',\n", "       'Sleep Hours Per Day', 'Heart Attack Risk', 'Sex', 'Diet'],\n", "      dtype='object')"]}, "execution_count": 162, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 163, "metadata": {}, "outputs": [], "source": ["pres=df['Blood Pressure']"]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [{"data": {"text/plain": ["0        158/88\n", "1        165/93\n", "2        174/99\n", "3       163/100\n", "4         91/88\n", "         ...   \n", "8758      94/76\n", "8759    157/102\n", "8760     161/75\n", "8761     119/67\n", "8762     138/67\n", "Name: Blood Pressure, Length: 8763, dtype: object"]}, "execution_count": 164, "metadata": {}, "output_type": "execute_result"}], "source": ["pres"]}, {"cell_type": "code", "execution_count": 165, "metadata": {}, "outputs": [], "source": ["systolic=pres.apply(lambda x : int(x.split('/')[0]))"]}, {"cell_type": "code", "execution_count": 166, "metadata": {}, "outputs": [{"data": {"text/plain": ["0       158\n", "1       165\n", "2       174\n", "3       163\n", "4        91\n", "       ... \n", "8758     94\n", "8759    157\n", "8760    161\n", "8761    119\n", "8762    138\n", "Name: Blood Pressure, Length: 8763, dtype: int64"]}, "execution_count": 166, "metadata": {}, "output_type": "execute_result"}], "source": ["systolic"]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [], "source": ["diastolic=systolic=pres.apply(lambda x : int(x.split('/')[1]))"]}, {"cell_type": "code", "execution_count": 168, "metadata": {}, "outputs": [{"data": {"text/plain": ["0        88\n", "1        93\n", "2        99\n", "3       100\n", "4        88\n", "       ... \n", "8758     76\n", "8759    102\n", "8760     75\n", "8761     67\n", "8762     67\n", "Name: Blood Pressure, Length: 8763, dtype: int64"]}, "execution_count": 168, "metadata": {}, "output_type": "execute_result"}], "source": ["diastolic"]}, {"cell_type": "code", "execution_count": 169, "metadata": {}, "outputs": [{"data": {"text/plain": ["0        158/88\n", "1        165/93\n", "2        174/99\n", "3       163/100\n", "4         91/88\n", "         ...   \n", "8758      94/76\n", "8759    157/102\n", "8760     161/75\n", "8761     119/67\n", "8762     138/67\n", "Name: Blood Pressure, Length: 8763, dtype: object"]}, "execution_count": 169, "metadata": {}, "output_type": "execute_result"}], "source": ["pres"]}, {"cell_type": "code", "execution_count": 170, "metadata": {}, "outputs": [{"data": {"text/plain": ["0        158/88\n", "1        165/93\n", "2        174/99\n", "3       163/100\n", "4         91/88\n", "         ...   \n", "8758      94/76\n", "8759    157/102\n", "8760     161/75\n", "8761     119/67\n", "8762     138/67\n", "Name: Blood Pressure, Length: 8763, dtype: object"]}, "execution_count": 170, "metadata": {}, "output_type": "execute_result"}], "source": ["df.pop('Blood Pressure')"]}, {"cell_type": "code", "execution_count": 171, "metadata": {}, "outputs": [], "source": ["df['Systolic']=systolic\n", "df['Diastolic']=diastolic"]}, {"cell_type": "code", "execution_count": 172, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Age', 'Cholesterol', 'Heart Rate', 'Diabetes', 'Family History',\n", "       'Smoking', 'Obesity', 'Alcohol Consumption', 'Exercise Hours Per Week',\n", "       'Previous Heart Problems', 'Medication Use', 'Stress Level',\n", "       'Sedentary Hours Per Day', 'Income', 'BMI', 'Triglycerides',\n", "       'Physical Activity Days Per Week', 'Sleep Hours Per Day',\n", "       'Heart Attack Risk', 'Sex', 'Diet', 'Systolic', 'Diastolic'],\n", "      dtype='object')"]}, "execution_count": 172, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 173, "metadata": {}, "outputs": [{"data": {"text/plain": ["(8763, 22)"]}, "execution_count": 173, "metadata": {}, "output_type": "execute_result"}], "source": ["y=df.pop('Heart Attack Risk')\n", "df.shape"]}, {"cell_type": "code", "execution_count": 174, "metadata": {}, "outputs": [{"data": {"text/plain": ["0       0\n", "1       0\n", "2       0\n", "3       0\n", "4       0\n", "       ..\n", "8758    0\n", "8759    0\n", "8760    1\n", "8761    0\n", "8762    1\n", "Name: Heart Attack Risk, Length: 8763, dtype: int64"]}, "execution_count": 174, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": 175, "metadata": {}, "outputs": [], "source": ["from sklearn.feature_selection import RFECV\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.model_selection import StratifiedKFold\n", "CV=StratifiedKFold(n_splits=5)\n", "LR=LogisticRegression()\n", "refc=RFECV(estimator=LR,cv=CV,min_features_to_select=22,verbose=True,n_jobs=-1)"]}, {"cell_type": "code", "execution_count": 176, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "x_train,x_test,y_train,y_test=train_test_split(df,y)"]}, {"cell_type": "code", "execution_count": 177, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\linear_model\\_logistic.py:465: ConvergenceWarning: lbfgs failed to converge (status=1):\n", "STOP: TOTAL NO. of ITERATIONS REACHED LIMIT.\n", "\n", "Increase the number of iterations (max_iter) or scale the data as shown in:\n", "    https://scikit-learn.org/stable/modules/preprocessing.html\n", "Please also refer to the documentation for alternative solver options:\n", "    https://scikit-learn.org/stable/modules/linear_model.html#logistic-regression\n", "  n_iter_i = _check_optimize_result(\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\linear_model\\_logistic.py:465: ConvergenceWarning: lbfgs failed to converge (status=1):\n", "STOP: TOTAL NO. of ITERATIONS REACHED LIMIT.\n", "\n", "Increase the number of iterations (max_iter) or scale the data as shown in:\n", "    https://scikit-learn.org/stable/modules/preprocessing.html\n", "Please also refer to the documentation for alternative solver options:\n", "    https://scikit-learn.org/stable/modules/linear_model.html#logistic-regression\n", "  n_iter_i = _check_optimize_result(\n"]}, {"data": {"text/html": ["<style>#sk-container-id-2 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-2 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-2 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-2 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-2 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-2 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-2 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-2 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-2 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-2 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-2 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-2 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-2 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-2 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-2 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-2 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-2 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RFECV(cv=StratifiedKFold(n_splits=5, random_state=None, shuffle=False),\n", "      estimator=LogisticRegression(), min_features_to_select=22, n_jobs=-1,\n", "      verbose=True)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" ><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>RFECV</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.6/modules/generated/sklearn.feature_selection.RFECV.html\">?<span>Documentation for RFECV</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\"><pre>RFECV(cv=StratifiedKFold(n_splits=5, random_state=None, shuffle=False),\n", "      estimator=LogisticRegression(), min_features_to_select=22, n_jobs=-1,\n", "      verbose=True)</pre></div> </div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-3\" type=\"checkbox\" ><label for=\"sk-estimator-id-3\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>estimator: LogisticRegression</div></div></label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression()</pre></div> </div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-4\" type=\"checkbox\" ><label for=\"sk-estimator-id-4\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>LogisticRegression</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.6/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a></div></label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression()</pre></div> </div></div></div></div></div></div></div></div></div>"], "text/plain": ["RFECV(cv=StratifiedKFold(n_splits=5, random_state=None, shuffle=False),\n", "      estimator=LogisticRegression(), min_features_to_select=22, n_jobs=-1,\n", "      verbose=True)"]}, "execution_count": 177, "metadata": {}, "output_type": "execute_result"}], "source": ["refc.fit(x_train,y_train)"]}, {"cell_type": "code", "execution_count": 178, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Age', 'Cholesterol', 'Heart Rate', 'Diabetes', 'Family History',\n", "       'Smoking', 'Obesity', 'Alcohol Consumption',\n", "       'Exercise Hours Per Week', 'Previous Heart Problems',\n", "       'Medication Use', 'Stress Level', 'Sedentary Hours Per Day',\n", "       'Income', 'BMI', 'Triglycerides',\n", "       'Physical Activity Days Per Week', 'Sleep Hours Per Day', 'Sex',\n", "       'Diet', 'Systolic', 'Diastolic'], dtype=object)"]}, "execution_count": 178, "metadata": {}, "output_type": "execute_result"}], "source": ["refc.get_feature_names_out()"]}, {"cell_type": "code", "execution_count": 179, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import confusion_matrix,accuracy_score\n", "pred=refc.predict(x_test)\n", "acc=accuracy_score(y_test,pred)\n", "conf=confusion_matrix(y_test,pred)"]}, {"cell_type": "code", "execution_count": 180, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1397,    0],\n", "       [ 794,    0]], dtype=int64)"]}, "execution_count": 180, "metadata": {}, "output_type": "execute_result"}], "source": ["conf"]}, {"cell_type": "code", "execution_count": 181, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.6376083979917846"]}, "execution_count": 181, "metadata": {}, "output_type": "execute_result"}], "source": ["acc"]}, {"cell_type": "code", "execution_count": 182, "metadata": {}, "outputs": [], "source": ["df['target']=y"]}, {"cell_type": "code", "execution_count": 183, "metadata": {}, "outputs": [], "source": ["df.to_csv(\"../datas/finaldataset.csv\")"]}, {"cell_type": "code", "execution_count": 184, "metadata": {}, "outputs": [{"data": {"text/plain": ["0       0\n", "1       0\n", "2       0\n", "3       0\n", "4       0\n", "       ..\n", "8758    0\n", "8759    0\n", "8760    1\n", "8761    0\n", "8762    1\n", "Name: target, Length: 8763, dtype: int64"]}, "execution_count": 184, "metadata": {}, "output_type": "execute_result"}], "source": ["df.pop('target')"]}, {"cell_type": "code", "execution_count": 185, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Cholesterol</th>\n", "      <th>Heart Rate</th>\n", "      <th>Diabetes</th>\n", "      <th>Family History</th>\n", "      <th>Smoking</th>\n", "      <th>Obesity</th>\n", "      <th>Alcohol Consumption</th>\n", "      <th>Exercise Hours Per Week</th>\n", "      <th>Previous Heart Problems</th>\n", "      <th>Medication Use</th>\n", "      <th>Stress Level</th>\n", "      <th>Sedentary Hours Per Day</th>\n", "      <th>Income</th>\n", "      <th>BMI</th>\n", "      <th>Triglycerides</th>\n", "      <th>Physical Activity Days Per Week</th>\n", "      <th>Sleep Hours Per Day</th>\n", "      <th>Sex</th>\n", "      <th>Diet</th>\n", "      <th>Systolic</th>\n", "      <th>Diast<PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67</td>\n", "      <td>208</td>\n", "      <td>72</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4.168189</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>6.615001</td>\n", "      <td>261404</td>\n", "      <td>31.251233</td>\n", "      <td>286</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>88</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21</td>\n", "      <td>389</td>\n", "      <td>98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.813242</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>4.963459</td>\n", "      <td>285768</td>\n", "      <td>27.194973</td>\n", "      <td>235</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>93</td>\n", "      <td>93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21</td>\n", "      <td>324</td>\n", "      <td>72</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2.078353</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>9.463426</td>\n", "      <td>235282</td>\n", "      <td>28.176571</td>\n", "      <td>587</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>99</td>\n", "      <td>99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>84</td>\n", "      <td>383</td>\n", "      <td>73</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>9.828130</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>7.648981</td>\n", "      <td>125640</td>\n", "      <td>36.464704</td>\n", "      <td>378</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>100</td>\n", "      <td>100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>66</td>\n", "      <td>318</td>\n", "      <td>93</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>5.804299</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>1.514821</td>\n", "      <td>160555</td>\n", "      <td>21.809144</td>\n", "      <td>231</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>88</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8758</th>\n", "      <td>60</td>\n", "      <td>121</td>\n", "      <td>61</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>7.917342</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>10.806373</td>\n", "      <td>235420</td>\n", "      <td>19.655895</td>\n", "      <td>67</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>76</td>\n", "      <td>76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8759</th>\n", "      <td>28</td>\n", "      <td>120</td>\n", "      <td>73</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>16.558426</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>3.833038</td>\n", "      <td>217881</td>\n", "      <td>23.993866</td>\n", "      <td>617</td>\n", "      <td>4</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>102</td>\n", "      <td>102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8760</th>\n", "      <td>47</td>\n", "      <td>250</td>\n", "      <td>105</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>3.148438</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>2.375214</td>\n", "      <td>36998</td>\n", "      <td>35.406146</td>\n", "      <td>527</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8761</th>\n", "      <td>36</td>\n", "      <td>178</td>\n", "      <td>60</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3.789950</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>0.029104</td>\n", "      <td>209943</td>\n", "      <td>27.294020</td>\n", "      <td>114</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>67</td>\n", "      <td>67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8762</th>\n", "      <td>25</td>\n", "      <td>356</td>\n", "      <td>75</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>18.081748</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "      <td>9.005234</td>\n", "      <td>247338</td>\n", "      <td>32.914151</td>\n", "      <td>180</td>\n", "      <td>7</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>67</td>\n", "      <td>67</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8763 rows × 22 columns</p>\n", "</div>"], "text/plain": ["      Age  Cholesterol  Heart Rate  Diabetes  ...  Sex  Diet  Systolic  Diastolic\n", "0      67          208          72         0  ...    1     0        88         88\n", "1      21          389          98         1  ...    1     2        93         93\n", "2      21          324          72         1  ...    0     1        99         99\n", "3      84          383          73         1  ...    1     0       100        100\n", "4      66          318          93         1  ...    1     2        88         88\n", "...   ...          ...         ...       ...  ...  ...   ...       ...        ...\n", "8758   60          121          61         1  ...    1     1        76         76\n", "8759   28          120          73         1  ...    0     1       102        102\n", "8760   47          250         105         0  ...    1     0        75         75\n", "8761   36          178          60         1  ...    1     2        67         67\n", "8762   25          356          75         1  ...    0     1        67         67\n", "\n", "[8763 rows x 22 columns]"]}, "execution_count": 185, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 186, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import StandardScaler\n", "SS=StandardScaler()\n", "final=SS.fit_transform(df)"]}, {"cell_type": "code", "execution_count": 187, "metadata": {}, "outputs": [], "source": ["df=pd.DataFrame(final,columns=df.columns)"]}, {"cell_type": "code", "execution_count": 188, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Cholesterol</th>\n", "      <th>Heart Rate</th>\n", "      <th>Diabetes</th>\n", "      <th>Family History</th>\n", "      <th>Smoking</th>\n", "      <th>Obesity</th>\n", "      <th>Alcohol Consumption</th>\n", "      <th>Exercise Hours Per Week</th>\n", "      <th>Previous Heart Problems</th>\n", "      <th>Medication Use</th>\n", "      <th>Stress Level</th>\n", "      <th>Sedentary Hours Per Day</th>\n", "      <th>Income</th>\n", "      <th>BMI</th>\n", "      <th>Triglycerides</th>\n", "      <th>Physical Activity Days Per Week</th>\n", "      <th>Sleep Hours Per Day</th>\n", "      <th>Sex</th>\n", "      <th>Diet</th>\n", "      <th>Systolic</th>\n", "      <th>Diast<PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.625557</td>\n", "      <td>-0.641579</td>\n", "      <td>-0.147042</td>\n", "      <td>-1.369651</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.010838</td>\n", "      <td>-0.991704</td>\n", "      <td>-0.996696</td>\n", "      <td>1.234604</td>\n", "      <td>0.179251</td>\n", "      <td>1.280130</td>\n", "      <td>0.373454</td>\n", "      <td>-0.588539</td>\n", "      <td>-1.528843</td>\n", "      <td>-0.514750</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>0.193782</td>\n", "      <td>0.193782</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-1.539322</td>\n", "      <td>1.596895</td>\n", "      <td>1.118179</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>-1.418027</td>\n", "      <td>1.008365</td>\n", "      <td>-0.996696</td>\n", "      <td>-1.563129</td>\n", "      <td>-0.297225</td>\n", "      <td>1.582523</td>\n", "      <td>-0.268479</td>\n", "      <td>-0.816487</td>\n", "      <td>-1.090738</td>\n", "      <td>-0.011823</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>0.534480</td>\n", "      <td>0.534480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-1.539322</td>\n", "      <td>0.793023</td>\n", "      <td>-0.147042</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>-2.948488</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.372188</td>\n", "      <td>1.008365</td>\n", "      <td>1.003315</td>\n", "      <td>1.234604</td>\n", "      <td>1.001031</td>\n", "      <td>0.955917</td>\n", "      <td>-0.113134</td>\n", "      <td>0.756800</td>\n", "      <td>0.223577</td>\n", "      <td>-1.520604</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>0.943319</td>\n", "      <td>0.943319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.425621</td>\n", "      <td>1.522691</td>\n", "      <td>-0.098380</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.032188</td>\n", "      <td>1.008365</td>\n", "      <td>-0.996696</td>\n", "      <td>1.234604</td>\n", "      <td>0.477557</td>\n", "      <td>-0.404902</td>\n", "      <td>1.198524</td>\n", "      <td>-0.177339</td>\n", "      <td>-0.214528</td>\n", "      <td>-1.520604</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>1.011458</td>\n", "      <td>1.011458</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.578495</td>\n", "      <td>0.718820</td>\n", "      <td>0.874867</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>-1.219867</td>\n", "      <td>-0.727941</td>\n", "      <td>1.008365</td>\n", "      <td>-0.996696</td>\n", "      <td>0.185454</td>\n", "      <td>-1.292170</td>\n", "      <td>0.028445</td>\n", "      <td>-1.120826</td>\n", "      <td>-0.834365</td>\n", "      <td>-1.090738</td>\n", "      <td>-1.017677</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>0.193782</td>\n", "      <td>0.193782</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8758</th>\n", "      <td>0.296119</td>\n", "      <td>-1.717530</td>\n", "      <td>-0.682328</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.362578</td>\n", "      <td>1.008365</td>\n", "      <td>1.003315</td>\n", "      <td>0.884887</td>\n", "      <td>1.388476</td>\n", "      <td>0.957630</td>\n", "      <td>-1.461594</td>\n", "      <td>-1.567374</td>\n", "      <td>1.537893</td>\n", "      <td>-0.011823</td>\n", "      <td>0.658765</td>\n", "      <td>0.002945</td>\n", "      <td>-0.623895</td>\n", "      <td>-0.623895</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8759</th>\n", "      <td>-1.209884</td>\n", "      <td>-1.729898</td>\n", "      <td>-0.098380</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>-2.948488</td>\n", "      <td>0.997151</td>\n", "      <td>-1.219867</td>\n", "      <td>1.131536</td>\n", "      <td>-0.991704</td>\n", "      <td>-0.996696</td>\n", "      <td>0.884887</td>\n", "      <td>-0.623356</td>\n", "      <td>0.739945</td>\n", "      <td>-0.775078</td>\n", "      <td>0.890887</td>\n", "      <td>0.223577</td>\n", "      <td>0.994032</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>1.147738</td>\n", "      <td>1.147738</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8760</th>\n", "      <td>-0.315695</td>\n", "      <td>-0.122154</td>\n", "      <td>1.458815</td>\n", "      <td>-1.369651</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>-1.187161</td>\n", "      <td>1.008365</td>\n", "      <td>-0.996696</td>\n", "      <td>-0.164263</td>\n", "      <td>-1.043943</td>\n", "      <td>-1.505080</td>\n", "      <td>1.030999</td>\n", "      <td>0.488626</td>\n", "      <td>0.223577</td>\n", "      <td>-1.520604</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>-0.692035</td>\n", "      <td>-0.692035</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8761</th>\n", "      <td>-0.833383</td>\n", "      <td>-1.012597</td>\n", "      <td>-0.730990</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.076238</td>\n", "      <td>1.008365</td>\n", "      <td>1.003315</td>\n", "      <td>-0.164263</td>\n", "      <td>-1.720804</td>\n", "      <td>0.641423</td>\n", "      <td>-0.252804</td>\n", "      <td>-1.357305</td>\n", "      <td>-0.652633</td>\n", "      <td>0.491104</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>-1.237152</td>\n", "      <td>-1.237152</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8762</th>\n", "      <td>-1.351072</td>\n", "      <td>1.188775</td>\n", "      <td>-0.001055</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>-2.948488</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>1.394931</td>\n", "      <td>-0.991704</td>\n", "      <td>-0.996696</td>\n", "      <td>0.884887</td>\n", "      <td>0.868841</td>\n", "      <td>1.105550</td>\n", "      <td>0.636623</td>\n", "      <td>-1.062313</td>\n", "      <td>1.537893</td>\n", "      <td>-1.520604</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>-1.237152</td>\n", "      <td>-1.237152</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8763 rows × 22 columns</p>\n", "</div>"], "text/plain": ["           Age  Cholesterol  Heart Rate  ...      Diet  Systolic  Diastolic\n", "0     0.625557    -0.641579   -0.147042  ... -1.225914  0.193782   0.193782\n", "1    -1.539322     1.596895    1.118179  ...  1.231804  0.534480   0.534480\n", "2    -1.539322     0.793023   -0.147042  ...  0.002945  0.943319   0.943319\n", "3     1.425621     1.522691   -0.098380  ... -1.225914  1.011458   1.011458\n", "4     0.578495     0.718820    0.874867  ...  1.231804  0.193782   0.193782\n", "...        ...          ...         ...  ...       ...       ...        ...\n", "8758  0.296119    -1.717530   -0.682328  ...  0.002945 -0.623895  -0.623895\n", "8759 -1.209884    -1.729898   -0.098380  ...  0.002945  1.147738   1.147738\n", "8760 -0.315695    -0.122154    1.458815  ... -1.225914 -0.692035  -0.692035\n", "8761 -0.833383    -1.012597   -0.730990  ...  1.231804 -1.237152  -1.237152\n", "8762 -1.351072     1.188775   -0.001055  ...  0.002945 -1.237152  -1.237152\n", "\n", "[8763 rows x 22 columns]"]}, "execution_count": 188, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 189, "metadata": {}, "outputs": [], "source": ["from sklearn.neighbors import KNeighborsClassifier\n", "KNN=KNeighborsClassifier()\n", "old=0\n", "n=0"]}, {"cell_type": "code", "execution_count": 190, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import confusion_matrix,accuracy_score\n", "pred=RF.predict(x_test)\n", "acc=accuracy_score(y_test,pred)\n", "conf=confusion_matrix(y_test,pred)"]}, {"cell_type": "code", "execution_count": 191, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9032405294386125"]}, "execution_count": 191, "metadata": {}, "output_type": "execute_result"}], "source": ["acc"]}, {"cell_type": "code", "execution_count": 193, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "x_train,x_test,y_train,y_test=train_test_split(df,y)\n", "for i in range(60,65):\n", "    KNN=KNeighborsClassifier(n_neighbors=i)\n", "    KNN.fit(x_train,y_train)\n", "    pred=KNN.predict(x_test)\n", "    acc=accuracy_score(y_test,pred)\n", "    conf=confusion_matrix(y_test,pred)\n", "    if(acc>old):\n", "        best=KNN\n", "        old=acc\n", "        n=i\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 195, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.6234596074851666"]}, "execution_count": 195, "metadata": {}, "output_type": "execute_result"}], "source": ["pred=best.predict(x_test)\n", "conf=confusion_matrix(y_test,pred)\n", "conf\n", "acc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 Age\n", "1 Cholesterol\n", "2 Heart Rate\n", "3 Diabetes\n", "4 Family History\n", "5 Smoking\n", "6 Obesity\n", "7 Alcohol Consumption\n", "8 Exercise Hours Per Week\n", "9 Previous Heart Problems\n", "10 Medication Use\n", "11 Stress Level\n", "12 Sedentary Hours Per Day\n", "13 Income\n", "14 BMI\n", "15 Triglycerides\n", "16 Physical Activity Days Per Week\n", "17 Sleep Hours Per Day\n", "18 Heart Attack Risk\n", "19 Sex\n", "20 Diet\n", "21 Systolic\n", "22 Diastolic\n"]}], "source": ["conf\n", "l=df.columns\n", "for i in range(len(l)):\n", "    print(str(i) +\" \"+ l[i])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["h=df.iloc[:,[0,1,2,3,4,5,6,9,14,15,19,21,22]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Age', 'Cholesterol', 'Heart Rate', 'Diabetes', 'Family History',\n", "       'Smoking', 'Obesity', 'Previous Heart Problems', 'BMI', 'Triglycerides',\n", "       'Sex', 'Systolic', 'Diastolic'],\n", "      dtype='object')"]}, "execution_count": 235, "metadata": {}, "output_type": "execute_result"}], "source": ["h.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.feature_selection import RFECV\n", "from sklearn.metrics import accuracy_score\n", "\n", "# Assuming df is your dataset and y is the target variable\n", "X = df.drop(columns=['y'])  # Features\n", "y = df['y']  # Target\n", "\n", "# Standardize the features\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "\n", "# Split the dataset\n", "X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)\n", "\n", "# Initialize KNN classifier\n", "knn = KNeighborsClassifier(n_neighbors=5)  # Default k=5\n", "\n", "# Perform RFECV (Recursive Feature Elimination with Cross-Validation)\n", "rfecv = RFECV(estimator=knn, step=1, cv=5, scoring='accuracy', n_jobs=-1)\n", "rfecv.fit(X_train, y_train)\n", "\n", "# Print the ranking of features\n", "feature_ranking = pd.DataFrame({'Feature': df.drop(columns=['y']).columns, 'Ranking': rfecv.ranking_})\n", "feature_ranking = feature_ranking.sort_values(by=\"Ranking\")\n", "print(\"\\nFeature Selection Process:\\n\", feature_ranking)\n", "\n", "# Plot the RFECV scores\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(range(1, len(rfecv.grid_scores_) + 1), rfecv.grid_scores_, marker='o', linestyle='dashed', color='b')\n", "plt.xlabel(\"Number of Selected Features\")\n", "plt.ylabel(\"Cross-Validation Accuracy\")\n", "plt.title(\"Feature Selection using RFECV\")\n", "plt.show()\n", "\n", "# Get the selected features\n", "selected_features = X.columns[rfecv.support_]\n", "print(\"\\nSelected Features:\", list(selected_features))\n", "\n", "# Train KNN with selected features\n", "X_train_selected = rfecv.transform(X_train)\n", "X_test_selected = rfecv.transform(X_test)\n", "\n", "knn.fit(X_train_selected, y_train)\n", "y_pred = knn.predict(X_test_selected)\n", "\n", "# Evaluate model performance\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(\"\\nModel Accuracy with Selected Features:\", accuracy)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Cholesterol</th>\n", "      <th>Heart Rate</th>\n", "      <th>Diabetes</th>\n", "      <th>Family History</th>\n", "      <th>Smoking</th>\n", "      <th>Obesity</th>\n", "      <th>Alcohol Consumption</th>\n", "      <th>Exercise Hours Per Week</th>\n", "      <th>Previous Heart Problems</th>\n", "      <th>...</th>\n", "      <th>Income</th>\n", "      <th>BMI</th>\n", "      <th>Triglycerides</th>\n", "      <th>Physical Activity Days Per Week</th>\n", "      <th>Sleep Hours Per Day</th>\n", "      <th>Heart Attack Risk</th>\n", "      <th>Sex</th>\n", "      <th>Diet</th>\n", "      <th>Systolic</th>\n", "      <th>Diast<PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67</td>\n", "      <td>208</td>\n", "      <td>72</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4.168189</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>261404</td>\n", "      <td>31.251233</td>\n", "      <td>286</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>88</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21</td>\n", "      <td>389</td>\n", "      <td>98</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.813242</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>285768</td>\n", "      <td>27.194973</td>\n", "      <td>235</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>93</td>\n", "      <td>93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21</td>\n", "      <td>324</td>\n", "      <td>72</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2.078353</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>235282</td>\n", "      <td>28.176571</td>\n", "      <td>587</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>99</td>\n", "      <td>99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>84</td>\n", "      <td>383</td>\n", "      <td>73</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>9.828130</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>125640</td>\n", "      <td>36.464704</td>\n", "      <td>378</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>100</td>\n", "      <td>100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>66</td>\n", "      <td>318</td>\n", "      <td>93</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>5.804299</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>160555</td>\n", "      <td>21.809144</td>\n", "      <td>231</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>88</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8758</th>\n", "      <td>60</td>\n", "      <td>121</td>\n", "      <td>61</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>7.917342</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>235420</td>\n", "      <td>19.655895</td>\n", "      <td>67</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>76</td>\n", "      <td>76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8759</th>\n", "      <td>28</td>\n", "      <td>120</td>\n", "      <td>73</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>16.558426</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>217881</td>\n", "      <td>23.993866</td>\n", "      <td>617</td>\n", "      <td>4</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>102</td>\n", "      <td>102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8760</th>\n", "      <td>47</td>\n", "      <td>250</td>\n", "      <td>105</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>3.148438</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>36998</td>\n", "      <td>35.406146</td>\n", "      <td>527</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8761</th>\n", "      <td>36</td>\n", "      <td>178</td>\n", "      <td>60</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3.789950</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>209943</td>\n", "      <td>27.294020</td>\n", "      <td>114</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>67</td>\n", "      <td>67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8762</th>\n", "      <td>25</td>\n", "      <td>356</td>\n", "      <td>75</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>18.081748</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>247338</td>\n", "      <td>32.914151</td>\n", "      <td>180</td>\n", "      <td>7</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>67</td>\n", "      <td>67</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8763 rows × 23 columns</p>\n", "</div>"], "text/plain": ["      Age  Cholesterol  Heart Rate  Diabetes  Family History  Smoking  \\\n", "0      67          208          72         0               0        1   \n", "1      21          389          98         1               1        1   \n", "2      21          324          72         1               0        0   \n", "3      84          383          73         1               1        1   \n", "4      66          318          93         1               1        1   \n", "...   ...          ...         ...       ...             ...      ...   \n", "8758   60          121          61         1               1        1   \n", "8759   28          120          73         1               0        0   \n", "8760   47          250         105         0               1        1   \n", "8761   36          178          60         1               0        1   \n", "8762   25          356          75         1               1        0   \n", "\n", "      Obesity  Alcohol Consumption  Exercise Hours Per Week  \\\n", "0           0                    0                 4.168189   \n", "1           1                    1                 1.813242   \n", "2           0                    0                 2.078353   \n", "3           0                    1                 9.828130   \n", "4           1                    0                 5.804299   \n", "...       ...                  ...                      ...   \n", "8758        0                    1                 7.917342   \n", "8759        1                    0                16.558426   \n", "8760        1                    1                 3.148438   \n", "8761        0                    0                 3.789950   \n", "8762        0                    1                18.081748   \n", "\n", "      Previous Heart Problems  ...  Income        BMI  Triglycerides  \\\n", "0                           0  ...  261404  31.251233            286   \n", "1                           1  ...  285768  27.194973            235   \n", "2                           1  ...  235282  28.176571            587   \n", "3                           1  ...  125640  36.464704            378   \n", "4                           1  ...  160555  21.809144            231   \n", "...                       ...  ...     ...        ...            ...   \n", "8758                        1  ...  235420  19.655895             67   \n", "8759                        0  ...  217881  23.993866            617   \n", "8760                        1  ...   36998  35.406146            527   \n", "8761                        1  ...  209943  27.294020            114   \n", "8762                        0  ...  247338  32.914151            180   \n", "\n", "      Physical Activity Days Per Week  Sleep Hours Per Day  Heart Attack Risk  \\\n", "0                                   0                    6                  0   \n", "1                                   1                    7                  0   \n", "2                                   4                    4                  0   \n", "3                                   3                    4                  0   \n", "4                                   1                    5                  0   \n", "...                               ...                  ...                ...   \n", "8758                                7                    7                  0   \n", "8759                                4                    9                  0   \n", "8760                                4                    4                  1   \n", "8761                                2                    8                  0   \n", "8762                                7                    4                  1   \n", "\n", "      Sex  Diet  Systolic  Diastolic  \n", "0       1     0        88         88  \n", "1       1     2        93         93  \n", "2       0     1        99         99  \n", "3       1     0       100        100  \n", "4       1     2        88         88  \n", "...   ...   ...       ...        ...  \n", "8758    1     1        76         76  \n", "8759    0     1       102        102  \n", "8760    1     0        75         75  \n", "8761    1     2        67         67  \n", "8762    0     1        67         67  \n", "\n", "[8763 rows x 23 columns]"]}, "execution_count": 287, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Cholesterol</th>\n", "      <th>Heart Rate</th>\n", "      <th>Diabetes</th>\n", "      <th>Family History</th>\n", "      <th>Smoking</th>\n", "      <th>Obesity</th>\n", "      <th>Alcohol Consumption</th>\n", "      <th>Exercise Hours Per Week</th>\n", "      <th>Previous Heart Problems</th>\n", "      <th>...</th>\n", "      <th>Income</th>\n", "      <th>BMI</th>\n", "      <th>Triglycerides</th>\n", "      <th>Physical Activity Days Per Week</th>\n", "      <th>Sleep Hours Per Day</th>\n", "      <th>Heart Attack Risk</th>\n", "      <th>Sex</th>\n", "      <th>Diet</th>\n", "      <th>Systolic</th>\n", "      <th>Diast<PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.625557</td>\n", "      <td>-0.641579</td>\n", "      <td>-0.147042</td>\n", "      <td>-1.369651</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.010838</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>1.280130</td>\n", "      <td>0.373454</td>\n", "      <td>-0.588539</td>\n", "      <td>-1.528843</td>\n", "      <td>-0.514750</td>\n", "      <td>-0.747090</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>0.193782</td>\n", "      <td>0.193782</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-1.539322</td>\n", "      <td>1.596895</td>\n", "      <td>1.118179</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>-1.418027</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>1.582523</td>\n", "      <td>-0.268479</td>\n", "      <td>-0.816487</td>\n", "      <td>-1.090738</td>\n", "      <td>-0.011823</td>\n", "      <td>-0.747090</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>0.534480</td>\n", "      <td>0.534480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-1.539322</td>\n", "      <td>0.793023</td>\n", "      <td>-0.147042</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>-2.948488</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.372188</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.955917</td>\n", "      <td>-0.113134</td>\n", "      <td>0.756800</td>\n", "      <td>0.223577</td>\n", "      <td>-1.520604</td>\n", "      <td>-0.747090</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>0.943319</td>\n", "      <td>0.943319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.425621</td>\n", "      <td>1.522691</td>\n", "      <td>-0.098380</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.032188</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>-0.404902</td>\n", "      <td>1.198524</td>\n", "      <td>-0.177339</td>\n", "      <td>-0.214528</td>\n", "      <td>-1.520604</td>\n", "      <td>-0.747090</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>1.011458</td>\n", "      <td>1.011458</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.578495</td>\n", "      <td>0.718820</td>\n", "      <td>0.874867</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>-1.219867</td>\n", "      <td>-0.727941</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.028445</td>\n", "      <td>-1.120826</td>\n", "      <td>-0.834365</td>\n", "      <td>-1.090738</td>\n", "      <td>-1.017677</td>\n", "      <td>-0.747090</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>0.193782</td>\n", "      <td>0.193782</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8758</th>\n", "      <td>0.296119</td>\n", "      <td>-1.717530</td>\n", "      <td>-0.682328</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.362578</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.957630</td>\n", "      <td>-1.461594</td>\n", "      <td>-1.567374</td>\n", "      <td>1.537893</td>\n", "      <td>-0.011823</td>\n", "      <td>-0.747090</td>\n", "      <td>0.658765</td>\n", "      <td>0.002945</td>\n", "      <td>-0.623895</td>\n", "      <td>-0.623895</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8759</th>\n", "      <td>-1.209884</td>\n", "      <td>-1.729898</td>\n", "      <td>-0.098380</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>-2.948488</td>\n", "      <td>0.997151</td>\n", "      <td>-1.219867</td>\n", "      <td>1.131536</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>0.739945</td>\n", "      <td>-0.775078</td>\n", "      <td>0.890887</td>\n", "      <td>0.223577</td>\n", "      <td>0.994032</td>\n", "      <td>-0.747090</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>1.147738</td>\n", "      <td>1.147738</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8760</th>\n", "      <td>-0.315695</td>\n", "      <td>-0.122154</td>\n", "      <td>1.458815</td>\n", "      <td>-1.369651</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>-1.187161</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>-1.505080</td>\n", "      <td>1.030999</td>\n", "      <td>0.488626</td>\n", "      <td>0.223577</td>\n", "      <td>-1.520604</td>\n", "      <td>1.338527</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>-0.692035</td>\n", "      <td>-0.692035</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8761</th>\n", "      <td>-0.833383</td>\n", "      <td>-1.012597</td>\n", "      <td>-0.730990</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.076238</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.641423</td>\n", "      <td>-0.252804</td>\n", "      <td>-1.357305</td>\n", "      <td>-0.652633</td>\n", "      <td>0.491104</td>\n", "      <td>-0.747090</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>-1.237152</td>\n", "      <td>-1.237152</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8762</th>\n", "      <td>-1.351072</td>\n", "      <td>1.188775</td>\n", "      <td>-0.001055</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>-2.948488</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>1.394931</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>1.105550</td>\n", "      <td>0.636623</td>\n", "      <td>-1.062313</td>\n", "      <td>1.537893</td>\n", "      <td>-1.520604</td>\n", "      <td>1.338527</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>-1.237152</td>\n", "      <td>-1.237152</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8763 rows × 23 columns</p>\n", "</div>"], "text/plain": ["           Age  Cholesterol  Heart Rate  Diabetes  Family History   Smoking  \\\n", "0     0.625557    -0.641579   -0.147042 -1.369651       -0.986061  0.339157   \n", "1    -1.539322     1.596895    1.118179  0.730113        1.014136  0.339157   \n", "2    -1.539322     0.793023   -0.147042  0.730113       -0.986061 -2.948488   \n", "3     1.425621     1.522691   -0.098380  0.730113        1.014136  0.339157   \n", "4     0.578495     0.718820    0.874867  0.730113        1.014136  0.339157   \n", "...        ...          ...         ...       ...             ...       ...   \n", "8758  0.296119    -1.717530   -0.682328  0.730113        1.014136  0.339157   \n", "8759 -1.209884    -1.729898   -0.098380  0.730113       -0.986061 -2.948488   \n", "8760 -0.315695    -0.122154    1.458815 -1.369651        1.014136  0.339157   \n", "8761 -0.833383    -1.012597   -0.730990  0.730113       -0.986061  0.339157   \n", "8762 -1.351072     1.188775   -0.001055  0.730113        1.014136 -2.948488   \n", "\n", "       Obesity  Alcohol Consumption  Exercise Hours Per Week  \\\n", "0    -1.002857            -1.219867                -1.010838   \n", "1     0.997151             0.819762                -1.418027   \n", "2    -1.002857            -1.219867                -1.372188   \n", "3    -1.002857             0.819762                -0.032188   \n", "4     0.997151            -1.219867                -0.727941   \n", "...        ...                  ...                      ...   \n", "8758 -1.002857             0.819762                -0.362578   \n", "8759  0.997151            -1.219867                 1.131536   \n", "8760  0.997151             0.819762                -1.187161   \n", "8761 -1.002857            -1.219867                -1.076238   \n", "8762 -1.002857             0.819762                 1.394931   \n", "\n", "      Previous Heart Problems  ...    Income       BMI  Triglycerides  \\\n", "0                   -0.991704  ...  1.280130  0.373454      -0.588539   \n", "1                    1.008365  ...  1.582523 -0.268479      -0.816487   \n", "2                    1.008365  ...  0.955917 -0.113134       0.756800   \n", "3                    1.008365  ... -0.404902  1.198524      -0.177339   \n", "4                    1.008365  ...  0.028445 -1.120826      -0.834365   \n", "...                       ...  ...       ...       ...            ...   \n", "8758                 1.008365  ...  0.957630 -1.461594      -1.567374   \n", "8759                -0.991704  ...  0.739945 -0.775078       0.890887   \n", "8760                 1.008365  ... -1.505080  1.030999       0.488626   \n", "8761                 1.008365  ...  0.641423 -0.252804      -1.357305   \n", "8762                -0.991704  ...  1.105550  0.636623      -1.062313   \n", "\n", "      Physical Activity Days Per Week  Sleep Hours Per Day  Heart Attack Risk  \\\n", "0                           -1.528843            -0.514750          -0.747090   \n", "1                           -1.090738            -0.011823          -0.747090   \n", "2                            0.223577            -1.520604          -0.747090   \n", "3                           -0.214528            -1.520604          -0.747090   \n", "4                           -1.090738            -1.017677          -0.747090   \n", "...                               ...                  ...                ...   \n", "8758                         1.537893            -0.011823          -0.747090   \n", "8759                         0.223577             0.994032          -0.747090   \n", "8760                         0.223577            -1.520604           1.338527   \n", "8761                        -0.652633             0.491104          -0.747090   \n", "8762                         1.537893            -1.520604           1.338527   \n", "\n", "           Sex      Diet  Systolic  Diastolic  \n", "0     0.658765 -1.225914  0.193782   0.193782  \n", "1     0.658765  1.231804  0.534480   0.534480  \n", "2    -1.517992  0.002945  0.943319   0.943319  \n", "3     0.658765 -1.225914  1.011458   1.011458  \n", "4     0.658765  1.231804  0.193782   0.193782  \n", "...        ...       ...       ...        ...  \n", "8758  0.658765  0.002945 -0.623895  -0.623895  \n", "8759 -1.517992  0.002945  1.147738   1.147738  \n", "8760  0.658765 -1.225914 -0.692035  -0.692035  \n", "8761  0.658765  1.231804 -1.237152  -1.237152  \n", "8762 -1.517992  0.002945 -1.237152  -1.237152  \n", "\n", "[8763 rows x 23 columns]"]}, "execution_count": 291, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'KNeighborsClassifier' object has no attribute 'passthrough'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m_RemoteTraceback\u001b[0m                          Traceback (most recent call last)", "\u001b[1;31m_RemoteTraceback\u001b[0m: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\externals\\loky\\process_executor.py\", line 463, in _process_worker\n    r = call_item()\n        ^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\externals\\loky\\process_executor.py\", line 291, in __call__\n    return self.fn(*self.args, **self.kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py\", line 598, in __call__\n    return [func(*args, **kwargs)\n            ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\utils\\parallel.py\", line 139, in __call__\n    return self.function(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\feature_selection\\_rfe.py\", line 52, in _rfe_single_fit\n    rfe._fit(\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\feature_selection\\_rfe.py\", line 335, in _fit\n    importances = _get_feature_importances(\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\feature_selection\\_base.py\", line 246, in _get_feature_importances\n    importances = getter(estimator)\n                  ^^^^^^^^^^^^^^^^^\nAttributeError: 'KNeighborsClassifier' object has no attribute 'passthrough'\n\"\"\"", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[293], line 21\u001b[0m\n\u001b[0;32m     19\u001b[0m \u001b[38;5;66;03m# Perform RFECV with importance_getter='passthrough'\u001b[39;00m\n\u001b[0;32m     20\u001b[0m rfecv \u001b[38;5;241m=\u001b[39m RFECV(estimator\u001b[38;5;241m=\u001b[39mknn, step\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m, cv\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m5\u001b[39m, scoring\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124maccuracy\u001b[39m\u001b[38;5;124m'\u001b[39m, importance_getter\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpassthrough\u001b[39m\u001b[38;5;124m'\u001b[39m, n_jobs\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[1;32m---> 21\u001b[0m \u001b[43mrfecv\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX_train\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_train\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     23\u001b[0m \u001b[38;5;66;03m# Print the ranking of features\u001b[39;00m\n\u001b[0;32m     24\u001b[0m feature_ranking \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataFrame({\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mFeature\u001b[39m\u001b[38;5;124m'\u001b[39m: df\u001b[38;5;241m.\u001b[39mdrop(columns\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124my\u001b[39m\u001b[38;5;124m'\u001b[39m])\u001b[38;5;241m.\u001b[39mcolumns, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mRanking\u001b[39m\u001b[38;5;124m'\u001b[39m: rfecv\u001b[38;5;241m.\u001b[39mranking_})\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\utils\\validation.py:63\u001b[0m, in \u001b[0;36m_deprecate_positional_args.<locals>._inner_deprecate_positional_args.<locals>.inner_f\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m     61\u001b[0m extra_args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlen\u001b[39m(args) \u001b[38;5;241m-\u001b[39m \u001b[38;5;28mlen\u001b[39m(all_args)\n\u001b[0;32m     62\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m extra_args \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m---> 63\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     65\u001b[0m \u001b[38;5;66;03m# extra_args > 0\u001b[39;00m\n\u001b[0;32m     66\u001b[0m args_msg \u001b[38;5;241m=\u001b[39m [\n\u001b[0;32m     67\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m=\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(name, arg)\n\u001b[0;32m     68\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m name, arg \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(kwonly_args[:extra_args], args[\u001b[38;5;241m-\u001b[39mextra_args:])\n\u001b[0;32m     69\u001b[0m ]\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\base.py:1389\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1382\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1384\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1385\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1386\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1387\u001b[0m     )\n\u001b[0;32m   1388\u001b[0m ):\n\u001b[1;32m-> 1389\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfit_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\feature_selection\\_rfe.py:873\u001b[0m, in \u001b[0;36mRFECV.fit\u001b[1;34m(self, X, y, groups, **params)\u001b[0m\n\u001b[0;32m    870\u001b[0m     parallel \u001b[38;5;241m=\u001b[39m Parallel(n_jobs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_jobs)\n\u001b[0;32m    871\u001b[0m     func \u001b[38;5;241m=\u001b[39m delayed(_rfe_single_fit)\n\u001b[1;32m--> 873\u001b[0m scores_features \u001b[38;5;241m=\u001b[39m \u001b[43mparallel\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    874\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mclone\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrfe\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtrain\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mscorer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrouted_params\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    875\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mtrain\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtest\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mcv\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mrouted_params\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplitter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplit\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    876\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    877\u001b[0m scores, step_n_features \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mzip\u001b[39m(\u001b[38;5;241m*\u001b[39mscores_features)\n\u001b[0;32m    879\u001b[0m step_n_features_rev \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(step_n_features[\u001b[38;5;241m0\u001b[39m])[::\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\utils\\parallel.py:77\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m     72\u001b[0m config \u001b[38;5;241m=\u001b[39m get_config()\n\u001b[0;32m     73\u001b[0m iterable_with_config \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m     74\u001b[0m     (_with_config(delayed_func, config), args, kwargs)\n\u001b[0;32m     75\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m delayed_func, args, kwargs \u001b[38;5;129;01min\u001b[39;00m iterable\n\u001b[0;32m     76\u001b[0m )\n\u001b[1;32m---> 77\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__call__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43miterable_with_config\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:2007\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m   2001\u001b[0m \u001b[38;5;66;03m# The first item from the output is blank, but it makes the interpreter\u001b[39;00m\n\u001b[0;32m   2002\u001b[0m \u001b[38;5;66;03m# progress until it enters the Try/Except block of the generator and\u001b[39;00m\n\u001b[0;32m   2003\u001b[0m \u001b[38;5;66;03m# reaches the first `yield` statement. This starts the asynchronous\u001b[39;00m\n\u001b[0;32m   2004\u001b[0m \u001b[38;5;66;03m# dispatch of the tasks to the workers.\u001b[39;00m\n\u001b[0;32m   2005\u001b[0m \u001b[38;5;28mnext\u001b[39m(output)\n\u001b[1;32m-> 2007\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m output \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mreturn_generator \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43moutput\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:1650\u001b[0m, in \u001b[0;36mParallel._get_outputs\u001b[1;34m(self, iterator, pre_dispatch)\u001b[0m\n\u001b[0;32m   1647\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m   1649\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend\u001b[38;5;241m.\u001b[39mretrieval_context():\n\u001b[1;32m-> 1650\u001b[0m         \u001b[38;5;28;01myield from\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retrieve()\n\u001b[0;32m   1652\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mGeneratorExit\u001b[39;00m:\n\u001b[0;32m   1653\u001b[0m     \u001b[38;5;66;03m# The generator has been garbage collected before being fully\u001b[39;00m\n\u001b[0;32m   1654\u001b[0m     \u001b[38;5;66;03m# consumed. This aborts the remaining tasks if possible and warn\u001b[39;00m\n\u001b[0;32m   1655\u001b[0m     \u001b[38;5;66;03m# the user if necessary.\u001b[39;00m\n\u001b[0;32m   1656\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:1754\u001b[0m, in \u001b[0;36mParallel._retrieve\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1747\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_wait_retrieval():\n\u001b[0;32m   1748\u001b[0m \n\u001b[0;32m   1749\u001b[0m     \u001b[38;5;66;03m# If the callback thread of a worker has signaled that its task\u001b[39;00m\n\u001b[0;32m   1750\u001b[0m     \u001b[38;5;66;03m# triggered an exception, or if the retrieval loop has raised an\u001b[39;00m\n\u001b[0;32m   1751\u001b[0m     \u001b[38;5;66;03m# exception (e.g. `GeneratorExit`), exit the loop and surface the\u001b[39;00m\n\u001b[0;32m   1752\u001b[0m     \u001b[38;5;66;03m# worker traceback.\u001b[39;00m\n\u001b[0;32m   1753\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_aborting:\n\u001b[1;32m-> 1754\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_error_fast\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1755\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[0;32m   1757\u001b[0m     \u001b[38;5;66;03m# If the next job is not ready for retrieval yet, we just wait for\u001b[39;00m\n\u001b[0;32m   1758\u001b[0m     \u001b[38;5;66;03m# async callbacks to progress.\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:1789\u001b[0m, in \u001b[0;36mParallel._raise_error_fast\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1785\u001b[0m \u001b[38;5;66;03m# If this error job exists, immediately raise the error by\u001b[39;00m\n\u001b[0;32m   1786\u001b[0m \u001b[38;5;66;03m# calling get_result. This job might not exists if abort has been\u001b[39;00m\n\u001b[0;32m   1787\u001b[0m \u001b[38;5;66;03m# called directly or if the generator is gc'ed.\u001b[39;00m\n\u001b[0;32m   1788\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m error_job \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m-> 1789\u001b[0m     \u001b[43merror_job\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_result\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:745\u001b[0m, in \u001b[0;36mBatchCompletionCallBack.get_result\u001b[1;34m(self, timeout)\u001b[0m\n\u001b[0;32m    739\u001b[0m backend \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mparallel\u001b[38;5;241m.\u001b[39m_backend\n\u001b[0;32m    741\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m backend\u001b[38;5;241m.\u001b[39msupports_retrieve_callback:\n\u001b[0;32m    742\u001b[0m     \u001b[38;5;66;03m# We assume that the result has already been retrieved by the\u001b[39;00m\n\u001b[0;32m    743\u001b[0m     \u001b[38;5;66;03m# callback thread, and is stored internally. It's just waiting to\u001b[39;00m\n\u001b[0;32m    744\u001b[0m     \u001b[38;5;66;03m# be returned.\u001b[39;00m\n\u001b[1;32m--> 745\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_return_or_raise\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    747\u001b[0m \u001b[38;5;66;03m# For other backends, the main thread needs to run the retrieval step.\u001b[39;00m\n\u001b[0;32m    748\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:763\u001b[0m, in \u001b[0;36mBatchCompletionCallBack._return_or_raise\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    761\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m    762\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstatus \u001b[38;5;241m==\u001b[39m TASK_ERROR:\n\u001b[1;32m--> 763\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result\n\u001b[0;32m    764\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result\n\u001b[0;32m    765\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "\u001b[1;31mAttributeError\u001b[0m: 'KNeighborsClassifier' object has no attribute 'passthrough'"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.feature_selection import RFECV\n", "from sklearn.metrics import accuracy_score\n", "\n", "# Assuming df is your dataset and y is the target variable\n", "\n", "\n", "# Split the dataset\n", "X_train, X_test, y_train, y_test = train_test_split(df, y, test_size=0.2, random_state=42)\n", "\n", "# Initialize KNN classifier\n", "knn = KNeighborsClassifier(n_neighbors=5)  # Default k=5\n", "\n", "# Perform RFECV with importance_getter='passthrough'\n", "rfecv = RFECV(estimator=knn, step=1, cv=5, scoring='accuracy', importance_getter='passthrough', n_jobs=-1)\n", "rfecv.fit(X_train, y_train)\n", "\n", "# Print the ranking of features\n", "feature_ranking = pd.DataFrame({'Feature': df.drop(columns=['y']).columns, 'Ranking': rfecv.ranking_})\n", "feature_ranking = feature_ranking.sort_values(by=\"Ranking\")\n", "print(\"\\nFeature Selection Process:\\n\", feature_ranking)\n", "\n", "# Plot the RFECV scores\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(range(1, len(rfecv.cv_results_['mean_test_score']) + 1), rfecv.cv_results_['mean_test_score'], marker='o', linestyle='dashed', color='b')\n", "plt.xlabel(\"Number of Selected Features\")\n", "plt.ylabel(\"Cross-Validation Accuracy\")\n", "plt.title(\"Feature Selection using RFECV with KNN\")\n", "plt.show()\n", "\n", "# Get the selected features\n", "selected_features = X.columns[rfecv.support_]\n", "print(\"\\nSelected Features:\", list(selected_features))\n", "\n", "# Train KNN with selected features\n", "X_train_selected = rfecv.transform(X_train)\n", "X_test_selected = rfecv.transform(X_test)\n", "\n", "knn.fit(X_train_selected, y_train)\n", "y_pred = knn.predict(X_test_selected)\n", "\n", "# Evaluate model performance\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(\"\\nModel Accuracy with Selected Features:\", accuracy)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "when `importance_getter=='auto'`, the underlying estimator KNeighborsClassifier should have `coef_` or `feature_importances_` attribute. Either pass a fitted estimator to feature selector or call fit before calling transform.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m_RemoteTraceback\u001b[0m                          Traceback (most recent call last)", "\u001b[1;31m_RemoteTraceback\u001b[0m: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\externals\\loky\\process_executor.py\", line 463, in _process_worker\n    r = call_item()\n        ^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\externals\\loky\\process_executor.py\", line 291, in __call__\n    return self.fn(*self.args, **self.kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py\", line 598, in __call__\n    return [func(*args, **kwargs)\n            ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\utils\\parallel.py\", line 139, in __call__\n    return self.function(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\feature_selection\\_rfe.py\", line 52, in _rfe_single_fit\n    rfe._fit(\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\feature_selection\\_rfe.py\", line 335, in _fit\n    importances = _get_feature_importances(\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\feature_selection\\_base.py\", line 234, in _get_feature_importances\n    raise ValueError(\nValueError: when `importance_getter=='auto'`, the underlying estimator KNeighborsClassifier should have `coef_` or `feature_importances_` attribute. Either pass a fitted estimator to feature selector or call fit before calling transform.\n\"\"\"", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[295], line 17\u001b[0m\n\u001b[0;32m     15\u001b[0m \u001b[38;5;66;03m# Perform RFECV for feature selection\u001b[39;00m\n\u001b[0;32m     16\u001b[0m rfecv \u001b[38;5;241m=\u001b[39m RFECV(estimator\u001b[38;5;241m=\u001b[39mknn, step\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m, cv\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m5\u001b[39m, scoring\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124maccuracy\u001b[39m\u001b[38;5;124m'\u001b[39m, n_jobs\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[1;32m---> 17\u001b[0m \u001b[43mrfecv\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX_train\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_train\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     19\u001b[0m \u001b[38;5;66;03m# Print the ranking of features\u001b[39;00m\n\u001b[0;32m     20\u001b[0m feature_ranking \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataFrame({\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mFeature\u001b[39m\u001b[38;5;124m'\u001b[39m: df\u001b[38;5;241m.\u001b[39mcolumns, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mRanking\u001b[39m\u001b[38;5;124m'\u001b[39m: rfecv\u001b[38;5;241m.\u001b[39mranking_})\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\utils\\validation.py:63\u001b[0m, in \u001b[0;36m_deprecate_positional_args.<locals>._inner_deprecate_positional_args.<locals>.inner_f\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m     61\u001b[0m extra_args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlen\u001b[39m(args) \u001b[38;5;241m-\u001b[39m \u001b[38;5;28mlen\u001b[39m(all_args)\n\u001b[0;32m     62\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m extra_args \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m---> 63\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     65\u001b[0m \u001b[38;5;66;03m# extra_args > 0\u001b[39;00m\n\u001b[0;32m     66\u001b[0m args_msg \u001b[38;5;241m=\u001b[39m [\n\u001b[0;32m     67\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m=\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(name, arg)\n\u001b[0;32m     68\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m name, arg \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(kwonly_args[:extra_args], args[\u001b[38;5;241m-\u001b[39mextra_args:])\n\u001b[0;32m     69\u001b[0m ]\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\base.py:1389\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1382\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1384\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1385\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1386\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1387\u001b[0m     )\n\u001b[0;32m   1388\u001b[0m ):\n\u001b[1;32m-> 1389\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfit_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\feature_selection\\_rfe.py:873\u001b[0m, in \u001b[0;36mRFECV.fit\u001b[1;34m(self, X, y, groups, **params)\u001b[0m\n\u001b[0;32m    870\u001b[0m     parallel \u001b[38;5;241m=\u001b[39m Parallel(n_jobs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_jobs)\n\u001b[0;32m    871\u001b[0m     func \u001b[38;5;241m=\u001b[39m delayed(_rfe_single_fit)\n\u001b[1;32m--> 873\u001b[0m scores_features \u001b[38;5;241m=\u001b[39m \u001b[43mparallel\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    874\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mclone\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrfe\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtrain\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mscorer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrouted_params\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    875\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mtrain\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtest\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mcv\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mrouted_params\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplitter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplit\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    876\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    877\u001b[0m scores, step_n_features \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mzip\u001b[39m(\u001b[38;5;241m*\u001b[39mscores_features)\n\u001b[0;32m    879\u001b[0m step_n_features_rev \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(step_n_features[\u001b[38;5;241m0\u001b[39m])[::\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\sklearn\\utils\\parallel.py:77\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m     72\u001b[0m config \u001b[38;5;241m=\u001b[39m get_config()\n\u001b[0;32m     73\u001b[0m iterable_with_config \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m     74\u001b[0m     (_with_config(delayed_func, config), args, kwargs)\n\u001b[0;32m     75\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m delayed_func, args, kwargs \u001b[38;5;129;01min\u001b[39;00m iterable\n\u001b[0;32m     76\u001b[0m )\n\u001b[1;32m---> 77\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__call__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43miterable_with_config\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:2007\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m   2001\u001b[0m \u001b[38;5;66;03m# The first item from the output is blank, but it makes the interpreter\u001b[39;00m\n\u001b[0;32m   2002\u001b[0m \u001b[38;5;66;03m# progress until it enters the Try/Except block of the generator and\u001b[39;00m\n\u001b[0;32m   2003\u001b[0m \u001b[38;5;66;03m# reaches the first `yield` statement. This starts the asynchronous\u001b[39;00m\n\u001b[0;32m   2004\u001b[0m \u001b[38;5;66;03m# dispatch of the tasks to the workers.\u001b[39;00m\n\u001b[0;32m   2005\u001b[0m \u001b[38;5;28mnext\u001b[39m(output)\n\u001b[1;32m-> 2007\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m output \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mreturn_generator \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43moutput\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:1650\u001b[0m, in \u001b[0;36mParallel._get_outputs\u001b[1;34m(self, iterator, pre_dispatch)\u001b[0m\n\u001b[0;32m   1647\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m   1649\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend\u001b[38;5;241m.\u001b[39mretrieval_context():\n\u001b[1;32m-> 1650\u001b[0m         \u001b[38;5;28;01myield from\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retrieve()\n\u001b[0;32m   1652\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mGeneratorExit\u001b[39;00m:\n\u001b[0;32m   1653\u001b[0m     \u001b[38;5;66;03m# The generator has been garbage collected before being fully\u001b[39;00m\n\u001b[0;32m   1654\u001b[0m     \u001b[38;5;66;03m# consumed. This aborts the remaining tasks if possible and warn\u001b[39;00m\n\u001b[0;32m   1655\u001b[0m     \u001b[38;5;66;03m# the user if necessary.\u001b[39;00m\n\u001b[0;32m   1656\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:1754\u001b[0m, in \u001b[0;36mParallel._retrieve\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1747\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_wait_retrieval():\n\u001b[0;32m   1748\u001b[0m \n\u001b[0;32m   1749\u001b[0m     \u001b[38;5;66;03m# If the callback thread of a worker has signaled that its task\u001b[39;00m\n\u001b[0;32m   1750\u001b[0m     \u001b[38;5;66;03m# triggered an exception, or if the retrieval loop has raised an\u001b[39;00m\n\u001b[0;32m   1751\u001b[0m     \u001b[38;5;66;03m# exception (e.g. `GeneratorExit`), exit the loop and surface the\u001b[39;00m\n\u001b[0;32m   1752\u001b[0m     \u001b[38;5;66;03m# worker traceback.\u001b[39;00m\n\u001b[0;32m   1753\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_aborting:\n\u001b[1;32m-> 1754\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_error_fast\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1755\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[0;32m   1757\u001b[0m     \u001b[38;5;66;03m# If the next job is not ready for retrieval yet, we just wait for\u001b[39;00m\n\u001b[0;32m   1758\u001b[0m     \u001b[38;5;66;03m# async callbacks to progress.\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:1789\u001b[0m, in \u001b[0;36mParallel._raise_error_fast\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1785\u001b[0m \u001b[38;5;66;03m# If this error job exists, immediately raise the error by\u001b[39;00m\n\u001b[0;32m   1786\u001b[0m \u001b[38;5;66;03m# calling get_result. This job might not exists if abort has been\u001b[39;00m\n\u001b[0;32m   1787\u001b[0m \u001b[38;5;66;03m# called directly or if the generator is gc'ed.\u001b[39;00m\n\u001b[0;32m   1788\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m error_job \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m-> 1789\u001b[0m     \u001b[43merror_job\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_result\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:745\u001b[0m, in \u001b[0;36mBatchCompletionCallBack.get_result\u001b[1;34m(self, timeout)\u001b[0m\n\u001b[0;32m    739\u001b[0m backend \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mparallel\u001b[38;5;241m.\u001b[39m_backend\n\u001b[0;32m    741\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m backend\u001b[38;5;241m.\u001b[39msupports_retrieve_callback:\n\u001b[0;32m    742\u001b[0m     \u001b[38;5;66;03m# We assume that the result has already been retrieved by the\u001b[39;00m\n\u001b[0;32m    743\u001b[0m     \u001b[38;5;66;03m# callback thread, and is stored internally. It's just waiting to\u001b[39;00m\n\u001b[0;32m    744\u001b[0m     \u001b[38;5;66;03m# be returned.\u001b[39;00m\n\u001b[1;32m--> 745\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_return_or_raise\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    747\u001b[0m \u001b[38;5;66;03m# For other backends, the main thread needs to run the retrieval step.\u001b[39;00m\n\u001b[0;32m    748\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\joblib\\parallel.py:763\u001b[0m, in \u001b[0;36mBatchCompletionCallBack._return_or_raise\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    761\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m    762\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstatus \u001b[38;5;241m==\u001b[39m TASK_ERROR:\n\u001b[1;32m--> 763\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result\n\u001b[0;32m    764\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_result\n\u001b[0;32m    765\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "\u001b[1;31mValueError\u001b[0m: when `importance_getter=='auto'`, the underlying estimator KNeighborsClassifier should have `coef_` or `feature_importances_` attribute. Either pass a fitted estimator to feature selector or call fit before calling transform."]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.feature_selection import RFECV\n", "from sklearn.metrics import accuracy_score\n", "\n", "# Split the dataset correctly\n", "X_train, X_test, y_train, y_test = train_test_split(df, y, test_size=0.2, random_state=42)\n", "\n", "# Initialize KNN classifier\n", "knn = KNeighborsClassifier(n_neighbors=5)  # Default k=5\n", "\n", "# Perform RFECV for feature selection\n", "rfecv = RFECV(estimator=knn, step=1, cv=5, scoring='accuracy', n_jobs=-1)\n", "rfecv.fit(X_train, y_train)\n", "\n", "# Print the ranking of features\n", "feature_ranking = pd.DataFrame({'Feature': df.columns, 'Ranking': rfecv.ranking_})\n", "feature_ranking = feature_ranking.sort_values(by=\"Ranking\")\n", "print(\"\\nFeature Selection Process:\\n\", feature_ranking)\n", "\n", "# Plot the RFECV scores\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(range(1, len(rfecv.cv_results_['mean_test_score']) + 1), rfecv.cv_results_['mean_test_score'], marker='o', linestyle='dashed', color='b')\n", "plt.xlabel(\"Number of Selected Features\")\n", "plt.ylabel(\"Cross-Validation Accuracy\")\n", "plt.title(\"Feature Selection using RFECV with KNN\")\n", "plt.show()\n", "\n", "# Get the selected features\n", "selected_features = df.columns[rfecv.support_]\n", "print(\"\\nSelected Features:\", list(selected_features))\n", "\n", "# Train KNN with selected features\n", "X_train_selected = rfecv.transform(X_train)\n", "X_test_selected = rfecv.transform(X_test)\n", "\n", "knn.fit(X_train_selected, y_train)\n", "y_pred = knn.predict(X_test_selected)\n", "\n", "# Evaluate model performance\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(\"\\nModel Accuracy with Selected Features:\", accuracy)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 8763 entries, 0 to 8762\n", "Data columns (total 22 columns):\n", " #   Column                           Non-Null Count  Dtype  \n", "---  ------                           --------------  -----  \n", " 0   Age                              8763 non-null   float64\n", " 1   Cholesterol                      8763 non-null   float64\n", " 2   Heart Rate                       8763 non-null   float64\n", " 3   Diabetes                         8763 non-null   float64\n", " 4   Family History                   8763 non-null   float64\n", " 5   Smoking                          8763 non-null   float64\n", " 6   Obesity                          8763 non-null   float64\n", " 7   Alcohol Consumption              8763 non-null   float64\n", " 8   Exercise Hours Per Week          8763 non-null   float64\n", " 9   Previous Heart Problems          8763 non-null   float64\n", " 10  Medication Use                   8763 non-null   float64\n", " 11  Stress Level                     8763 non-null   float64\n", " 12  Sedentary Hours Per Day          8763 non-null   float64\n", " 13  Income                           8763 non-null   float64\n", " 14  BMI                              8763 non-null   float64\n", " 15  Triglycerides                    8763 non-null   float64\n", " 16  Physical Activity Days Per Week  8763 non-null   float64\n", " 17  Sleep Hours Per Day              8763 non-null   float64\n", " 18  Sex                              8763 non-null   float64\n", " 19  Diet                             8763 non-null   float64\n", " 20  Systolic                         8763 non-null   float64\n", " 21  Diastolic                        8763 non-null   float64\n", "dtypes: float64(22)\n", "memory usage: 1.5 MB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0       0\n", "1       0\n", "2       0\n", "3       0\n", "4       0\n", "       ..\n", "8758    0\n", "8759    0\n", "8760    1\n", "8761    0\n", "8762    1\n", "Name: Heart Attack Risk, Length: 8763, dtype: int64"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Age', 'Cholesterol', 'Heart Rate', 'Diabetes', 'Family History',\n", "       'Smoking', 'Obesity', 'Alcohol Consumption', 'Exercise Hours Per Week',\n", "       'Previous Heart Problems', 'Medication Use', 'Stress Level',\n", "       'Sedentary Hours Per Day', 'Income', 'BMI', 'Triglycerides',\n", "       'Physical Activity Days Per Week', 'Sleep Hours Per Day', 'Sex', 'Diet',\n", "       'Systolic', 'Diastolic'],\n", "      dtype='object')"]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x_train,x_test,y_train,y_test=train_test_split(df,y,test_size=0.20)\n", "from sklearn.metrics import confusion_matrix\n", "from sklearn.neighbors import KNeighborsClassifier\n", "knn=KNeighborsClassifier()\n", "knn.fit(x_train,y_train)\n", "pred=knn.predict(x_test)\n", "acc=accuracy_score(y_test,pred)\n", "conf=confusion_matrix(y_test,pred)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.5670279520821448"]}, "execution_count": 141, "metadata": {}, "output_type": "execute_result"}], "source": ["acc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'KNN' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[139], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mKNN\u001b[49m\u001b[38;5;241m.\u001b[39mpredict\n", "\u001b[1;31mNameError\u001b[0m: name 'KNN' is not defined"]}], "source": ["KNN.predict"]}, {"cell_type": "code", "execution_count": 196, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Cholesterol</th>\n", "      <th>Heart Rate</th>\n", "      <th>Diabetes</th>\n", "      <th>Family History</th>\n", "      <th>Smoking</th>\n", "      <th>Obesity</th>\n", "      <th>Alcohol Consumption</th>\n", "      <th>Exercise Hours Per Week</th>\n", "      <th>Previous Heart Problems</th>\n", "      <th>Medication Use</th>\n", "      <th>Stress Level</th>\n", "      <th>Sedentary Hours Per Day</th>\n", "      <th>Income</th>\n", "      <th>BMI</th>\n", "      <th>Triglycerides</th>\n", "      <th>Physical Activity Days Per Week</th>\n", "      <th>Sleep Hours Per Day</th>\n", "      <th>Sex</th>\n", "      <th>Diet</th>\n", "      <th>Systolic</th>\n", "      <th>Diast<PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.625557</td>\n", "      <td>-0.641579</td>\n", "      <td>-0.147042</td>\n", "      <td>-1.369651</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.010838</td>\n", "      <td>-0.991704</td>\n", "      <td>-0.996696</td>\n", "      <td>1.234604</td>\n", "      <td>0.179251</td>\n", "      <td>1.280130</td>\n", "      <td>0.373454</td>\n", "      <td>-0.588539</td>\n", "      <td>-1.528843</td>\n", "      <td>-0.514750</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>0.193782</td>\n", "      <td>0.193782</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-1.539322</td>\n", "      <td>1.596895</td>\n", "      <td>1.118179</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>-1.418027</td>\n", "      <td>1.008365</td>\n", "      <td>-0.996696</td>\n", "      <td>-1.563129</td>\n", "      <td>-0.297225</td>\n", "      <td>1.582523</td>\n", "      <td>-0.268479</td>\n", "      <td>-0.816487</td>\n", "      <td>-1.090738</td>\n", "      <td>-0.011823</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>0.534480</td>\n", "      <td>0.534480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-1.539322</td>\n", "      <td>0.793023</td>\n", "      <td>-0.147042</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>-2.948488</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.372188</td>\n", "      <td>1.008365</td>\n", "      <td>1.003315</td>\n", "      <td>1.234604</td>\n", "      <td>1.001031</td>\n", "      <td>0.955917</td>\n", "      <td>-0.113134</td>\n", "      <td>0.756800</td>\n", "      <td>0.223577</td>\n", "      <td>-1.520604</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>0.943319</td>\n", "      <td>0.943319</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.425621</td>\n", "      <td>1.522691</td>\n", "      <td>-0.098380</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.032188</td>\n", "      <td>1.008365</td>\n", "      <td>-0.996696</td>\n", "      <td>1.234604</td>\n", "      <td>0.477557</td>\n", "      <td>-0.404902</td>\n", "      <td>1.198524</td>\n", "      <td>-0.177339</td>\n", "      <td>-0.214528</td>\n", "      <td>-1.520604</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>1.011458</td>\n", "      <td>1.011458</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.578495</td>\n", "      <td>0.718820</td>\n", "      <td>0.874867</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>-1.219867</td>\n", "      <td>-0.727941</td>\n", "      <td>1.008365</td>\n", "      <td>-0.996696</td>\n", "      <td>0.185454</td>\n", "      <td>-1.292170</td>\n", "      <td>0.028445</td>\n", "      <td>-1.120826</td>\n", "      <td>-0.834365</td>\n", "      <td>-1.090738</td>\n", "      <td>-1.017677</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>0.193782</td>\n", "      <td>0.193782</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8758</th>\n", "      <td>0.296119</td>\n", "      <td>-1.717530</td>\n", "      <td>-0.682328</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.362578</td>\n", "      <td>1.008365</td>\n", "      <td>1.003315</td>\n", "      <td>0.884887</td>\n", "      <td>1.388476</td>\n", "      <td>0.957630</td>\n", "      <td>-1.461594</td>\n", "      <td>-1.567374</td>\n", "      <td>1.537893</td>\n", "      <td>-0.011823</td>\n", "      <td>0.658765</td>\n", "      <td>0.002945</td>\n", "      <td>-0.623895</td>\n", "      <td>-0.623895</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8759</th>\n", "      <td>-1.209884</td>\n", "      <td>-1.729898</td>\n", "      <td>-0.098380</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>-2.948488</td>\n", "      <td>0.997151</td>\n", "      <td>-1.219867</td>\n", "      <td>1.131536</td>\n", "      <td>-0.991704</td>\n", "      <td>-0.996696</td>\n", "      <td>0.884887</td>\n", "      <td>-0.623356</td>\n", "      <td>0.739945</td>\n", "      <td>-0.775078</td>\n", "      <td>0.890887</td>\n", "      <td>0.223577</td>\n", "      <td>0.994032</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>1.147738</td>\n", "      <td>1.147738</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8760</th>\n", "      <td>-0.315695</td>\n", "      <td>-0.122154</td>\n", "      <td>1.458815</td>\n", "      <td>-1.369651</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>-1.187161</td>\n", "      <td>1.008365</td>\n", "      <td>-0.996696</td>\n", "      <td>-0.164263</td>\n", "      <td>-1.043943</td>\n", "      <td>-1.505080</td>\n", "      <td>1.030999</td>\n", "      <td>0.488626</td>\n", "      <td>0.223577</td>\n", "      <td>-1.520604</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>-0.692035</td>\n", "      <td>-0.692035</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8761</th>\n", "      <td>-0.833383</td>\n", "      <td>-1.012597</td>\n", "      <td>-0.730990</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.076238</td>\n", "      <td>1.008365</td>\n", "      <td>1.003315</td>\n", "      <td>-0.164263</td>\n", "      <td>-1.720804</td>\n", "      <td>0.641423</td>\n", "      <td>-0.252804</td>\n", "      <td>-1.357305</td>\n", "      <td>-0.652633</td>\n", "      <td>0.491104</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>-1.237152</td>\n", "      <td>-1.237152</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8762</th>\n", "      <td>-1.351072</td>\n", "      <td>1.188775</td>\n", "      <td>-0.001055</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>-2.948488</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>1.394931</td>\n", "      <td>-0.991704</td>\n", "      <td>-0.996696</td>\n", "      <td>0.884887</td>\n", "      <td>0.868841</td>\n", "      <td>1.105550</td>\n", "      <td>0.636623</td>\n", "      <td>-1.062313</td>\n", "      <td>1.537893</td>\n", "      <td>-1.520604</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>-1.237152</td>\n", "      <td>-1.237152</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8763 rows × 22 columns</p>\n", "</div>"], "text/plain": ["           Age  Cholesterol  Heart Rate  ...      Diet  Systolic  Diastolic\n", "0     0.625557    -0.641579   -0.147042  ... -1.225914  0.193782   0.193782\n", "1    -1.539322     1.596895    1.118179  ...  1.231804  0.534480   0.534480\n", "2    -1.539322     0.793023   -0.147042  ...  0.002945  0.943319   0.943319\n", "3     1.425621     1.522691   -0.098380  ... -1.225914  1.011458   1.011458\n", "4     0.578495     0.718820    0.874867  ...  1.231804  0.193782   0.193782\n", "...        ...          ...         ...  ...       ...       ...        ...\n", "8758  0.296119    -1.717530   -0.682328  ...  0.002945 -0.623895  -0.623895\n", "8759 -1.209884    -1.729898   -0.098380  ...  0.002945  1.147738   1.147738\n", "8760 -0.315695    -0.122154    1.458815  ... -1.225914 -0.692035  -0.692035\n", "8761 -0.833383    -1.012597   -0.730990  ...  1.231804 -1.237152  -1.237152\n", "8762 -1.351072     1.188775   -0.001055  ...  0.002945 -1.237152  -1.237152\n", "\n", "[8763 rows x 22 columns]"]}, "execution_count": 196, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 198, "metadata": {}, "outputs": [], "source": ["df['Heart Attack Risk']=y\n", "df.to_csv(\"../datas/scaleddata.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}