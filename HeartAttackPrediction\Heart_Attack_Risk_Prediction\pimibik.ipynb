{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: vonage in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (4.4.0)\n", "Requirement already satisfied: vonage-utils>=1.1.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.1.4)\n", "Requirement already satisfied: vonage-http-client>=1.5.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.5.1)\n", "Requirement already satisfied: vonage-account>=1.1.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.1.1)\n", "Requirement already satisfied: vonage-application>=2.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (2.0.1)\n", "Requirement already satisfied: vonage-messages>=1.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.4.0)\n", "Requirement already satisfied: vonage-network-auth>=1.0.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.2)\n", "Requirement already satisfied: vonage-network-sim-swap>=1.1.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.1.2)\n", "Requirement already satisfied: vonage-network-number-verification>=1.0.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.2)\n", "Requirement already satisfied: vonage-number-insight>=1.0.6 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.6)\n", "Requirement already satisfied: vonage-numbers>=1.0.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.4)\n", "Requirement already satisfied: vonage-sms>=1.1.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.1.5)\n", "Requirement already satisfied: vonage-subaccounts>=1.0.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.4)\n", "Requirement already satisfied: vonage-users>=1.2.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.2.1)\n", "Requirement already satisfied: vonage-verify>=2.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (2.1.0)\n", "Requirement already satisfied: vonage-verify-legacy>=1.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.1)\n", "Requirement already satisfied: vonage-video>=1.2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.2.0)\n", "Requirement already satisfied: vonage-voice>=1.3.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.3.0)\n", "Requirement already satisfied: pydantic>=2.9.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage-account>=1.1.1->vonage) (2.11.2)\n", "Requirement already satisfied: vonage-jwt>=1.1.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage-http-client>=1.5.1->vonage) (1.1.5)\n", "Requirement already satisfied: requests>=2.27.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage-http-client>=1.5.1->vonage) (2.32.2)\n", "Requirement already satisfied: typing-extensions>=4.9.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage-http-client>=1.5.1->vonage) (4.13.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic>=2.9.2->vonage-account>=1.1.1->vonage) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic>=2.9.2->vonage-account>=1.1.1->vonage) (2.33.1)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic>=2.9.2->vonage-account>=1.1.1->vonage) (0.4.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.27.0->vonage-http-client>=1.5.1->vonage) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.27.0->vonage-http-client>=1.5.1->vonage) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.27.0->vonage-http-client>=1.5.1->vonage) (2.2.1)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.27.0->vonage-http-client>=1.5.1->vonage) (2024.2.2)\n", "Requirement already satisfied: pyjwt>=1.6.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyjwt[crypto]>=1.6.4->vonage-jwt>=1.1.5->vonage-http-client>=1.5.1->vonage) (2.10.1)\n", "Requirement already satisfied: cryptography>=3.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyjwt[crypto]>=1.6.4->vonage-jwt>=1.1.5->vonage-http-client>=1.5.1->vonage) (44.0.2)\n", "Requirement already satisfied: cffi>=1.12 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from cryptography>=3.4.0->pyjwt[crypto]>=1.6.4->vonage-jwt>=1.1.5->vonage-http-client>=1.5.1->vonage) (1.16.0)\n", "Requirement already satisfied: pycparser in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from cffi>=1.12->cryptography>=3.4.0->pyjwt[crypto]>=1.6.4->vonage-jwt>=1.1.5->vonage-http-client>=1.5.1->vonage) (2.22)\n"]}, {"ename": "NameError", "evalue": "name 'vonage' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m get_ipython()\u001b[38;5;241m.\u001b[39msystem(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpip install vonage\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m----> 2\u001b[0m client \u001b[38;5;241m=\u001b[39m \u001b[43mvonage\u001b[49m\u001b[38;5;241m.\u001b[39mClient(key\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbe59a37a\u001b[39m\u001b[38;5;124m\"\u001b[39m, secret\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mP0uN1Hxz8m4AIzmb\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m      3\u001b[0m sms \u001b[38;5;241m=\u001b[39m vonage\u001b[38;5;241m.\u001b[39mSms(client)\n\u001b[0;32m      4\u001b[0m responseData \u001b[38;5;241m=\u001b[39m sms\u001b[38;5;241m.\u001b[39msend_message(responseData \u001b[38;5;241m=\u001b[39m sms\u001b[38;5;241m.\u001b[39msend_message(\n\u001b[0;32m      5\u001b[0m     {\n\u001b[0;32m      6\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfrom\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mVonage APIs\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     10\u001b[0m )\n\u001b[0;32m     11\u001b[0m )\n", "\u001b[1;31mNameError\u001b[0m: name 'vonage' is not defined"]}], "source": ["!pip install vonage\n", "client = vonage.Client(key=\"be59a37a\", secret=\"P0uN1Hxz8m4AIzmb\")\n", "sms = vonage.Sms(client)\n", "responseData = sms.send_message(responseData = sms.send_message(\n", "    {\n", "        \"from\": \"Vonage APIs\",\n", "        \"to\": \"918825677072\",\n", "        \"text\": \"A text message sent using the Nexmo SMS API\",\n", "    }\n", ")\n", ")\n", "\n", "if (responseData[\"messages\"][0][\"status\"] == \"0\"):\n", "    print(\"Message sent successfully.\")\n", "else:\n", "    print(f\"Message failed with error: {responseData['messages'][0]['error-text']}\")\n", "    {\n", "        \"from\": \"Vonage APIs\",\n", "        \"to\": \"918825677072\",\n", "        \"text\": \"A text message sent using the Nexmo SMS API\",\n", "    }\n", "\n", "if responseData[\"messages\"][0][\"status\"] == \"0\":\n", "    print(\"Message sent successfully.\")\n", "else:\n", "    print(f\"Message failed with error: {responseData['messages'][0]['error-text']}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: vonage in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (4.4.0)\n", "Requirement already satisfied: vonage-utils>=1.1.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.1.4)\n", "Requirement already satisfied: vonage-http-client>=1.5.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.5.1)\n", "Requirement already satisfied: vonage-account>=1.1.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.1.1)\n", "Requirement already satisfied: vonage-application>=2.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (2.0.1)\n", "Requirement already satisfied: vonage-messages>=1.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.4.0)\n", "Requirement already satisfied: vonage-network-auth>=1.0.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.2)\n", "Requirement already satisfied: vonage-network-sim-swap>=1.1.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.1.2)\n", "Requirement already satisfied: vonage-network-number-verification>=1.0.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.2)\n", "Requirement already satisfied: vonage-number-insight>=1.0.6 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.6)\n", "Requirement already satisfied: vonage-numbers>=1.0.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.4)\n", "Requirement already satisfied: vonage-sms>=1.1.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.1.5)\n", "Requirement already satisfied: vonage-subaccounts>=1.0.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.4)\n", "Requirement already satisfied: vonage-users>=1.2.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.2.1)\n", "Requirement already satisfied: vonage-verify>=2.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (2.1.0)\n", "Requirement already satisfied: vonage-verify-legacy>=1.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.0.1)\n", "Requirement already satisfied: vonage-video>=1.2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.2.0)\n", "Requirement already satisfied: vonage-voice>=1.3.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage) (1.3.0)\n", "Requirement already satisfied: pydantic>=2.9.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage-account>=1.1.1->vonage) (2.11.2)\n", "Requirement already satisfied: vonage-jwt>=1.1.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage-http-client>=1.5.1->vonage) (1.1.5)\n", "Requirement already satisfied: requests>=2.27.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage-http-client>=1.5.1->vonage) (2.32.2)\n", "Requirement already satisfied: typing-extensions>=4.9.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from vonage-http-client>=1.5.1->vonage) (4.13.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic>=2.9.2->vonage-account>=1.1.1->vonage) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic>=2.9.2->vonage-account>=1.1.1->vonage) (2.33.1)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic>=2.9.2->vonage-account>=1.1.1->vonage) (0.4.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.27.0->vonage-http-client>=1.5.1->vonage) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.27.0->vonage-http-client>=1.5.1->vonage) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.27.0->vonage-http-client>=1.5.1->vonage) (2.2.1)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.27.0->vonage-http-client>=1.5.1->vonage) (2024.2.2)\n", "Requirement already satisfied: pyjwt>=1.6.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyjwt[crypto]>=1.6.4->vonage-jwt>=1.1.5->vonage-http-client>=1.5.1->vonage) (2.10.1)\n", "Requirement already satisfied: cryptography>=3.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyjwt[crypto]>=1.6.4->vonage-jwt>=1.1.5->vonage-http-client>=1.5.1->vonage) (44.0.2)\n", "Requirement already satisfied: cffi>=1.12 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from cryptography>=3.4.0->pyjwt[crypto]>=1.6.4->vonage-jwt>=1.1.5->vonage-http-client>=1.5.1->vonage) (1.16.0)\n", "Requirement already satisfied: pycparser in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from cffi>=1.12->cryptography>=3.4.0->pyjwt[crypto]>=1.6.4->vonage-jwt>=1.1.5->vonage-http-client>=1.5.1->vonage) (2.22)\n"]}], "source": ["!pip install vonage\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Account', 'Application', 'Auth', 'HttpClientOptions', 'Messages', 'NetworkNumberVerification', 'NetworkSimSwap', 'NumberInsight', 'Numbers', 'Sms', 'Subaccounts', 'Users', 'Verify', 'VerifyLegacy', 'Video', 'Voice', 'Vonage', 'VonageError', '__all__', '__builtins__', '__cached__', '__doc__', '__file__', '__loader__', '__name__', '__package__', '__path__', '__spec__', '_version', 'vonage']\n"]}], "source": ["import vonage\n", "print(dir(vonage))  # Lists available attributes and functions\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'vonage' has no attribute 'client'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[13], line 4\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m# Initialize client\u001b[39;00m\n\u001b[1;32m----> 4\u001b[0m client \u001b[38;5;241m=\u001b[39m \u001b[43mvonage\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclient\u001b[49m(key\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbe59a37a\u001b[39m\u001b[38;5;124m\"\u001b[39m, secret\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mP0uN1Hxz8m4AIzmb\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m      5\u001b[0m sms \u001b[38;5;241m=\u001b[39m vonage\u001b[38;5;241m.\u001b[39mSms(client)\n\u001b[0;32m      7\u001b[0m \u001b[38;5;66;03m# Send SMS\u001b[39;00m\n", "\u001b[1;31mAttributeError\u001b[0m: module 'vonage' has no attribute 'client'"]}], "source": ["import vonage\n", "\n", "# Initialize client\n", "client = vonage.client(key=\"be59a37a\", secret=\"P0uN1Hxz8m4AIzmb\")\n", "sms = vonage.Sms(client)\n", "\n", "# Send SMS\n", "responseData = sms.send_message(\n", "    {\n", "        \"from\": \"Vonage APIs\",\n", "        \"to\": \"918825677072\",\n", "        \"text\": \"A text message sent using the Nexmo SMS API\",\n", "    }\n", ")\n", "\n", "# Check response\n", "if responseData[\"messages\"][0][\"status\"] == \"0\":\n", "    print(\"Message sent successfully.\")\n", "else:\n", "    print(f\"Message failed with error: {responseData['messages'][0]['error-text']}\")\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'vonage' has no attribute 'Client'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 4\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m# Create a client\u001b[39;00m\n\u001b[1;32m----> 4\u001b[0m client \u001b[38;5;241m=\u001b[39m \u001b[43mvonage\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mClient\u001b[49m(\n\u001b[0;32m      5\u001b[0m     application_id\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124myour_application_id\u001b[39m\u001b[38;5;124m\"\u001b[39m,  \u001b[38;5;66;03m# If using OAuth\u001b[39;00m\n\u001b[0;32m      6\u001b[0m     private_key\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpath/to/your/private.key\u001b[39m\u001b[38;5;124m\"\u001b[39m  \u001b[38;5;66;03m# If using OAuth\u001b[39;00m\n\u001b[0;32m      7\u001b[0m )\n\u001b[0;32m      9\u001b[0m sms \u001b[38;5;241m=\u001b[39m vonage\u001b[38;5;241m.\u001b[39mSms(client)\n\u001b[0;32m     11\u001b[0m \u001b[38;5;66;03m# Send an SMS\u001b[39;00m\n", "\u001b[1;31mAttributeError\u001b[0m: module 'vonage' has no attribute 'Client'"]}], "source": ["import vonage\n", "\n", "# Create a client\n", "client = vonage.Client(\n", "    application_id=\"your_application_id\",  # If using OAuth\n", "    private_key=\"path/to/your/private.key\"  # If using OAuth\n", ")\n", "\n", "sms = vonage.Sms(client)\n", "\n", "# Send an SMS\n", "responseData = sms.send_message(\n", "    {\n", "        \"from\": \"Vonage APIs\",\n", "        \"to\": \"918825677072\",\n", "        \"text\": \"Hello from Von<PERSON>!\",\n", "    }\n", ")\n", "\n", "# Check response\n", "if responseData[\"messages\"][0][\"status\"] == \"0\":\n", "    print(\"Message sent successfully.\")\n", "else:\n", "    print(f\"Message failed with error: {responseData['messages'][0]['error-text']}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}