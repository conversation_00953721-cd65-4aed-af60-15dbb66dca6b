[build]
  base = "HeartAttackPrediction/Heart_Attack_Risk_Prediction"
  publish = "HeartAttackPrediction/Heart_Attack_Risk_Prediction/dist"
  command = "npm run build"

[build.environment]
  PYTHON_VERSION = "3.11"

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[functions]
  directory = "HeartAttackPrediction/Heart_Attack_Risk_Prediction/netlify/functions"
  
[dev]
  command = "npm run dev"
  port = 3000
  publish = "HeartAttackPrediction/Heart_Attack_Risk_Prediction/dist"
