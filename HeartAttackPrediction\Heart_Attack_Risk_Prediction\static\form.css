* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    min-height: 100vh;
    background: linear-gradient(135deg, #4568dc, #b06ab3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

nav {
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-brand {
    color: white;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 600;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: #ff6b6b;
}

.container {
    max-width: 600px;
    width: 90%;
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    margin-top: 80px;
}

.prediction-form h2 {
    text-align: center;
    color: #4568dc;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    border-color: #4568dc;
    outline: none;
}

.submit-btn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 5px;
    width: 100%;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    background: #45a049;
}
