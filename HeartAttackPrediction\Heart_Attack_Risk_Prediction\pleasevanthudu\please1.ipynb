{"cells": [{"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "newdf=pd.read_csv(\"../datas/Medicaldataset.csv\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["<bound method NDFrame.head of       Age  Gender  Heart rate  Systolic blood pressure  \\\n", "0      64       1          66                      160   \n", "1      21       1          94                       98   \n", "2      55       1          64                      160   \n", "3      64       1          70                      120   \n", "4      55       1          64                      112   \n", "...   ...     ...         ...                      ...   \n", "1314   44       1          94                      122   \n", "1315   66       1          84                      125   \n", "1316   45       1          85                      168   \n", "1317   54       1          58                      117   \n", "1318   51       1          94                      157   \n", "\n", "      Diastolic blood pressure  Blood sugar  CK-MB  Troponin    Result  \n", "0                           83        160.0   1.80     0.012  negative  \n", "1                           46        296.0   6.75     1.060  positive  \n", "2                           77        270.0   1.99     0.003  negative  \n", "3                           55        270.0  13.87     0.122  positive  \n", "4                           65        300.0   1.08     0.003  negative  \n", "...                        ...          ...    ...       ...       ...  \n", "1314                        67        204.0   1.63     0.006  negative  \n", "1315                        55        149.0   1.33     0.172  positive  \n", "1316                       104         96.0   1.24     4.250  positive  \n", "1317                        68        443.0   5.80     0.359  positive  \n", "1318                        79        134.0  50.89     1.770  positive  \n", "\n", "[1319 rows x 9 columns]>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["newdf.head"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1319 entries, 0 to 1318\n", "Data columns (total 9 columns):\n", " #   Column                    Non-Null Count  Dtype  \n", "---  ------                    --------------  -----  \n", " 0   Age                       1319 non-null   int64  \n", " 1   Gender                    1319 non-null   int64  \n", " 2   Heart rate                1319 non-null   int64  \n", " 3   Systolic blood pressure   1319 non-null   int64  \n", " 4   Diastolic blood pressure  1319 non-null   int64  \n", " 5   Blood sugar               1319 non-null   float64\n", " 6   CK-MB                     1319 non-null   float64\n", " 7   Troponin                  1319 non-null   float64\n", " 8   Result                    1319 non-null   object \n", "dtypes: float64(3), int64(5), object(1)\n", "memory usage: 92.9+ KB\n"]}], "source": ["newdf.info()"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["result=pd.get_dummies(data=newdf,columns=['Result'],drop_first=True,dtype='int64')"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import LabelEncoder\n", "lab=LabelEncoder()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["result=newdf['Result']"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["Result=lab.fit_transform(result)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 0, ..., 1, 1, 1])"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["Result"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["0       negative\n", "1       positive\n", "2       negative\n", "3       positive\n", "4       negative\n", "          ...   \n", "1314    negative\n", "1315    positive\n", "1316    positive\n", "1317    positive\n", "1318    positive\n", "Name: Result, Length: 1319, dtype: object"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["newdf.pop('Result')"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["newdf['Result']=Result"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["Result\n", "1    810\n", "0    509\n", "Name: count, dtype: int64"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["newdf['Result'].value_counts()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier\n", "lr=RandomForestClassifier()\n", "from sklearn.model_selection import train_test_split\n", "y=newdf.pop('Result') \n", "x_train,x_test,y_train,y_test=train_test_split(newdf,y)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-2 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-2 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-2 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-2 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-2 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-2 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-2 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-2 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-2 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-2 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-2 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-2 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-2 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-2 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-2 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-2 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-2 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomForestClassifier()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>RandomForestClassifier</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.6/modules/generated/sklearn.ensemble.RandomForestClassifier.html\">?<span>Documentation for RandomForestClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestClassifier()</pre></div> </div></div></div></div>"], "text/plain": ["RandomForestClassifier()"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["lr.fit(x_train,y_train)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import accuracy_score,confusion_matrix\n", "pred=lr.predict(x_test)\n", "acc=accuracy_score(y_test,pred)\n", "conf=confusion_matrix(y_test,pred)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.990909090909091"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["acc"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[124,   2],\n", "       [  1, 203]], dtype=int64)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["conf"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["# import joblib\n", "# joblib.dump(lr,\"perfectmodel.joblib\")"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Gender</th>\n", "      <th>Heart rate</th>\n", "      <th>Systolic blood pressure</th>\n", "      <th>Diastolic blood pressure</th>\n", "      <th>Blood sugar</th>\n", "      <th>CK-MB</th>\n", "      <th>Troponin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>64</td>\n", "      <td>1</td>\n", "      <td>66</td>\n", "      <td>160</td>\n", "      <td>83</td>\n", "      <td>160.0</td>\n", "      <td>1.80</td>\n", "      <td>0.012</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21</td>\n", "      <td>1</td>\n", "      <td>94</td>\n", "      <td>98</td>\n", "      <td>46</td>\n", "      <td>296.0</td>\n", "      <td>6.75</td>\n", "      <td>1.060</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>55</td>\n", "      <td>1</td>\n", "      <td>64</td>\n", "      <td>160</td>\n", "      <td>77</td>\n", "      <td>270.0</td>\n", "      <td>1.99</td>\n", "      <td>0.003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>64</td>\n", "      <td>1</td>\n", "      <td>70</td>\n", "      <td>120</td>\n", "      <td>55</td>\n", "      <td>270.0</td>\n", "      <td>13.87</td>\n", "      <td>0.122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>55</td>\n", "      <td>1</td>\n", "      <td>64</td>\n", "      <td>112</td>\n", "      <td>65</td>\n", "      <td>300.0</td>\n", "      <td>1.08</td>\n", "      <td>0.003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1314</th>\n", "      <td>44</td>\n", "      <td>1</td>\n", "      <td>94</td>\n", "      <td>122</td>\n", "      <td>67</td>\n", "      <td>204.0</td>\n", "      <td>1.63</td>\n", "      <td>0.006</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1315</th>\n", "      <td>66</td>\n", "      <td>1</td>\n", "      <td>84</td>\n", "      <td>125</td>\n", "      <td>55</td>\n", "      <td>149.0</td>\n", "      <td>1.33</td>\n", "      <td>0.172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1316</th>\n", "      <td>45</td>\n", "      <td>1</td>\n", "      <td>85</td>\n", "      <td>168</td>\n", "      <td>104</td>\n", "      <td>96.0</td>\n", "      <td>1.24</td>\n", "      <td>4.250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1317</th>\n", "      <td>54</td>\n", "      <td>1</td>\n", "      <td>58</td>\n", "      <td>117</td>\n", "      <td>68</td>\n", "      <td>443.0</td>\n", "      <td>5.80</td>\n", "      <td>0.359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1318</th>\n", "      <td>51</td>\n", "      <td>1</td>\n", "      <td>94</td>\n", "      <td>157</td>\n", "      <td>79</td>\n", "      <td>134.0</td>\n", "      <td>50.89</td>\n", "      <td>1.770</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1319 rows × 8 columns</p>\n", "</div>"], "text/plain": ["      Age  Gender  Heart rate  Systolic blood pressure  \\\n", "0      64       1          66                      160   \n", "1      21       1          94                       98   \n", "2      55       1          64                      160   \n", "3      64       1          70                      120   \n", "4      55       1          64                      112   \n", "...   ...     ...         ...                      ...   \n", "1314   44       1          94                      122   \n", "1315   66       1          84                      125   \n", "1316   45       1          85                      168   \n", "1317   54       1          58                      117   \n", "1318   51       1          94                      157   \n", "\n", "      Diastolic blood pressure  Blood sugar  CK-MB  Troponin  \n", "0                           83        160.0   1.80     0.012  \n", "1                           46        296.0   6.75     1.060  \n", "2                           77        270.0   1.99     0.003  \n", "3                           55        270.0  13.87     0.122  \n", "4                           65        300.0   1.08     0.003  \n", "...                        ...          ...    ...       ...  \n", "1314                        67        204.0   1.63     0.006  \n", "1315                        55        149.0   1.33     0.172  \n", "1316                       104         96.0   1.24     4.250  \n", "1317                        68        443.0   5.80     0.359  \n", "1318                        79        134.0  50.89     1.770  \n", "\n", "[1319 rows x 8 columns]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["newdf"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Age', 'Gender', 'Heart rate', 'Systolic blood pressure',\n", "       'Diastolic blood pressure', 'Blood sugar', 'CK-MB', 'Troponin'],\n", "      dtype='object')"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["newdf.columns"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["new=[65, 1, 110, 190, 125, 250, 10, 0.08]"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "finaltesting=np.array(new).reshape(1,-1)"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["finaltesting=pd.DataFrame(finaltesting,columns=newdf.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0     65.00\n", "1      1.00\n", "2    110.00\n", "3    190.00\n", "4    125.00\n", "5    250.00\n", "6     10.00\n", "7      0.08\n", "dtype: float64"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["testt=lr.predict(finaltesting)"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1])"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["testt"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1319, 8)"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["newdf.shape"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Gender</th>\n", "      <th>Heart rate</th>\n", "      <th>Systolic blood pressure</th>\n", "      <th>Diastolic blood pressure</th>\n", "      <th>Blood sugar</th>\n", "      <th>CK-MB</th>\n", "      <th>Troponin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>65.0</td>\n", "      <td>1.0</td>\n", "      <td>110.0</td>\n", "      <td>190.0</td>\n", "      <td>125.0</td>\n", "      <td>250.0</td>\n", "      <td>10.0</td>\n", "      <td>0.08</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Age  Gender  Heart rate  Systolic blood pressure  \\\n", "0  65.0     1.0       110.0                    190.0   \n", "\n", "   Diastolic blood pressure  Blood sugar  CK-MB  Troponin  \n", "0                     125.0        250.0   10.0      0.08  "]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["finaltesting"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["['finalfinalmodel.joblib']"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["import joblib\n", "joblib.dump(lr,\"finalfinalmodel.joblib\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}