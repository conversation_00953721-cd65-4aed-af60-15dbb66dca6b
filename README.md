# Heart Attack Risk Prediction System

A machine learning-powered web application that predicts heart attack risk based on medical parameters.

## 🚀 Live Demo

Visit the deployed application: [Your Netlify URL]

## 📁 Project Structure

```
├── HeartAttackPrediction/
│   └── Heart_Attack_Risk_Prediction/    # Main application directory
│       ├── app.py                       # Flask application
│       ├── requirements.txt             # Python dependencies
│       ├── package.json                 # Build configuration
│       ├── netlify.toml                 # Netlify settings
│       ├── finalfinalmodel.joblib       # ML model
│       ├── netlify/functions/           # Serverless functions
│       ├── templates/                   # HTML templates
│       ├── static/                      # CSS, JS, images
│       └── README_NEW.md               # Detailed documentation
├── netlify.toml                        # Root Netlify configuration
├── package.json                        # Root build scripts
└── README.md                          # This file
```

## 🛠️ Quick Deploy to Netlify

1. **Fork this repository**
2. **Connect to Netlify:**
   - Go to [netlify.com](https://netlify.com)
   - Click "New site from Git"
   - Select your forked repository
3. **Build settings are automatically configured** via `netlify.toml`
4. **Add environment variables** in Netlify dashboard:
   ```
   SECRET_KEY=your-secret-key
   TWILIO_SID=your-twilio-sid
   TWILIO_TOKEN=your-twilio-token
   TWILIO_FROM_NUMBER=+1234567890
   TWILIO_TO_NUMBER=+1234567890
   ```
5. **Deploy!**

## 📖 Detailed Documentation

For complete setup instructions, API documentation, and development guide, see:
[HeartAttackPrediction/Heart_Attack_Risk_Prediction/README_NEW.md](HeartAttackPrediction/Heart_Attack_Risk_Prediction/README_NEW.md)

## 🔧 Local Development

```bash
cd HeartAttackPrediction/Heart_Attack_Risk_Prediction
pip install -r requirements.txt
python app.py
```

## 🌟 Features

- ✅ Heart attack risk prediction using ML
- ✅ Interactive web interface
- ✅ Real-time risk assessment
- ✅ SMS notifications (optional)
- ✅ Historical data dashboard
- ✅ Responsive design
- ✅ Serverless deployment ready

## 📄 License

MIT License - see detailed documentation for more information.
