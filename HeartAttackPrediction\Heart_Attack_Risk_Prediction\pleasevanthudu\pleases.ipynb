{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sklearn as sk"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0          0\n", "1          1\n", "2          2\n", "3          3\n", "4          4\n", "        ... \n", "8758    8758\n", "8759    8759\n", "8760    8760\n", "8761    8761\n", "8762    8762\n", "Name: Unnamed: 0, Length: 8763, dtype: int64"]}, "execution_count": 224, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.7090701654306902"]}, "execution_count": 225, "metadata": {}, "output_type": "execute_result"}], "source": ["y=df['Heart Attack Risk']\n", "df.pop('Unnamed: 0')\n", "from sklearn.model_selection import train_test_split\n", "x_train,x_test,y_train,y_test=train_test_split(df,y,test_size=0.20)\n", "from sklearn.neighbors import KNeighborsClassifier\n", "model=KNeighborsClassifier(n_neighbors=30)\n", "model.fit(x_train,y_train)\n", "pred=model.predict(x_test)\n", "from sklearn.metrics import accuracy_score,confusion_matrix\n", "acc=accuracy_score(y_test,pred)\n", "conf=confusion_matrix(y_test,pred)\n", "conf\n", "acc"]}, {"cell_type": "code", "execution_count": 226, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Cholesterol</th>\n", "      <th>Heart Rate</th>\n", "      <th>Diabetes</th>\n", "      <th>Family History</th>\n", "      <th>Smoking</th>\n", "      <th>Obesity</th>\n", "      <th>Alcohol Consumption</th>\n", "      <th>Exercise Hours Per Week</th>\n", "      <th>Previous Heart Problems</th>\n", "      <th>...</th>\n", "      <th>Income</th>\n", "      <th>BMI</th>\n", "      <th>Triglycerides</th>\n", "      <th>Physical Activity Days Per Week</th>\n", "      <th>Sleep Hours Per Day</th>\n", "      <th>Sex</th>\n", "      <th>Diet</th>\n", "      <th>Systolic</th>\n", "      <th>Diast<PERSON></th>\n", "      <th>Heart Attack Risk</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.625557</td>\n", "      <td>-0.641579</td>\n", "      <td>-0.147042</td>\n", "      <td>-1.369651</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.010838</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>1.280130</td>\n", "      <td>0.373454</td>\n", "      <td>-0.588539</td>\n", "      <td>-1.528843</td>\n", "      <td>-0.514750</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>0.193782</td>\n", "      <td>0.193782</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-1.539322</td>\n", "      <td>1.596895</td>\n", "      <td>1.118179</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>-1.418027</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>1.582523</td>\n", "      <td>-0.268479</td>\n", "      <td>-0.816487</td>\n", "      <td>-1.090738</td>\n", "      <td>-0.011823</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>0.534480</td>\n", "      <td>0.534480</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-1.539322</td>\n", "      <td>0.793023</td>\n", "      <td>-0.147042</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>-2.948488</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.372188</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.955917</td>\n", "      <td>-0.113134</td>\n", "      <td>0.756800</td>\n", "      <td>0.223577</td>\n", "      <td>-1.520604</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>0.943319</td>\n", "      <td>0.943319</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.425621</td>\n", "      <td>1.522691</td>\n", "      <td>-0.098380</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.032188</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>-0.404902</td>\n", "      <td>1.198524</td>\n", "      <td>-0.177339</td>\n", "      <td>-0.214528</td>\n", "      <td>-1.520604</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>1.011458</td>\n", "      <td>1.011458</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.578495</td>\n", "      <td>0.718820</td>\n", "      <td>0.874867</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>-1.219867</td>\n", "      <td>-0.727941</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.028445</td>\n", "      <td>-1.120826</td>\n", "      <td>-0.834365</td>\n", "      <td>-1.090738</td>\n", "      <td>-1.017677</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>0.193782</td>\n", "      <td>0.193782</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8758</th>\n", "      <td>0.296119</td>\n", "      <td>-1.717530</td>\n", "      <td>-0.682328</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.362578</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.957630</td>\n", "      <td>-1.461594</td>\n", "      <td>-1.567374</td>\n", "      <td>1.537893</td>\n", "      <td>-0.011823</td>\n", "      <td>0.658765</td>\n", "      <td>0.002945</td>\n", "      <td>-0.623895</td>\n", "      <td>-0.623895</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8759</th>\n", "      <td>-1.209884</td>\n", "      <td>-1.729898</td>\n", "      <td>-0.098380</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>-2.948488</td>\n", "      <td>0.997151</td>\n", "      <td>-1.219867</td>\n", "      <td>1.131536</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>0.739945</td>\n", "      <td>-0.775078</td>\n", "      <td>0.890887</td>\n", "      <td>0.223577</td>\n", "      <td>0.994032</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>1.147738</td>\n", "      <td>1.147738</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8760</th>\n", "      <td>-0.315695</td>\n", "      <td>-0.122154</td>\n", "      <td>1.458815</td>\n", "      <td>-1.369651</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>-1.187161</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>-1.505080</td>\n", "      <td>1.030999</td>\n", "      <td>0.488626</td>\n", "      <td>0.223577</td>\n", "      <td>-1.520604</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>-0.692035</td>\n", "      <td>-0.692035</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8761</th>\n", "      <td>-0.833383</td>\n", "      <td>-1.012597</td>\n", "      <td>-0.730990</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.076238</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.641423</td>\n", "      <td>-0.252804</td>\n", "      <td>-1.357305</td>\n", "      <td>-0.652633</td>\n", "      <td>0.491104</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>-1.237152</td>\n", "      <td>-1.237152</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8762</th>\n", "      <td>-1.351072</td>\n", "      <td>1.188775</td>\n", "      <td>-0.001055</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>-2.948488</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>1.394931</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>1.105550</td>\n", "      <td>0.636623</td>\n", "      <td>-1.062313</td>\n", "      <td>1.537893</td>\n", "      <td>-1.520604</td>\n", "      <td>-1.517992</td>\n", "      <td>0.002945</td>\n", "      <td>-1.237152</td>\n", "      <td>-1.237152</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8763 rows × 23 columns</p>\n", "</div>"], "text/plain": ["           Age  Cholesterol  Heart Rate  Diabetes  Family History   Smoking  \\\n", "0     0.625557    -0.641579   -0.147042 -1.369651       -0.986061  0.339157   \n", "1    -1.539322     1.596895    1.118179  0.730113        1.014136  0.339157   \n", "2    -1.539322     0.793023   -0.147042  0.730113       -0.986061 -2.948488   \n", "3     1.425621     1.522691   -0.098380  0.730113        1.014136  0.339157   \n", "4     0.578495     0.718820    0.874867  0.730113        1.014136  0.339157   \n", "...        ...          ...         ...       ...             ...       ...   \n", "8758  0.296119    -1.717530   -0.682328  0.730113        1.014136  0.339157   \n", "8759 -1.209884    -1.729898   -0.098380  0.730113       -0.986061 -2.948488   \n", "8760 -0.315695    -0.122154    1.458815 -1.369651        1.014136  0.339157   \n", "8761 -0.833383    -1.012597   -0.730990  0.730113       -0.986061  0.339157   \n", "8762 -1.351072     1.188775   -0.001055  0.730113        1.014136 -2.948488   \n", "\n", "       Obesity  Alcohol Consumption  Exercise Hours Per Week  \\\n", "0    -1.002857            -1.219867                -1.010838   \n", "1     0.997151             0.819762                -1.418027   \n", "2    -1.002857            -1.219867                -1.372188   \n", "3    -1.002857             0.819762                -0.032188   \n", "4     0.997151            -1.219867                -0.727941   \n", "...        ...                  ...                      ...   \n", "8758 -1.002857             0.819762                -0.362578   \n", "8759  0.997151            -1.219867                 1.131536   \n", "8760  0.997151             0.819762                -1.187161   \n", "8761 -1.002857            -1.219867                -1.076238   \n", "8762 -1.002857             0.819762                 1.394931   \n", "\n", "      Previous Heart Problems  ...    Income       BMI  Triglycerides  \\\n", "0                   -0.991704  ...  1.280130  0.373454      -0.588539   \n", "1                    1.008365  ...  1.582523 -0.268479      -0.816487   \n", "2                    1.008365  ...  0.955917 -0.113134       0.756800   \n", "3                    1.008365  ... -0.404902  1.198524      -0.177339   \n", "4                    1.008365  ...  0.028445 -1.120826      -0.834365   \n", "...                       ...  ...       ...       ...            ...   \n", "8758                 1.008365  ...  0.957630 -1.461594      -1.567374   \n", "8759                -0.991704  ...  0.739945 -0.775078       0.890887   \n", "8760                 1.008365  ... -1.505080  1.030999       0.488626   \n", "8761                 1.008365  ...  0.641423 -0.252804      -1.357305   \n", "8762                -0.991704  ...  1.105550  0.636623      -1.062313   \n", "\n", "      Physical Activity Days Per Week  Sleep Hours Per Day       Sex  \\\n", "0                           -1.528843            -0.514750  0.658765   \n", "1                           -1.090738            -0.011823  0.658765   \n", "2                            0.223577            -1.520604 -1.517992   \n", "3                           -0.214528            -1.520604  0.658765   \n", "4                           -1.090738            -1.017677  0.658765   \n", "...                               ...                  ...       ...   \n", "8758                         1.537893            -0.011823  0.658765   \n", "8759                         0.223577             0.994032 -1.517992   \n", "8760                         0.223577            -1.520604  0.658765   \n", "8761                        -0.652633             0.491104  0.658765   \n", "8762                         1.537893            -1.520604 -1.517992   \n", "\n", "          Diet  Systolic  Diastolic  Heart Attack Risk  \n", "0    -1.225914  0.193782   0.193782                  0  \n", "1     1.231804  0.534480   0.534480                  0  \n", "2     0.002945  0.943319   0.943319                  0  \n", "3    -1.225914  1.011458   1.011458                  0  \n", "4     1.231804  0.193782   0.193782                  0  \n", "...        ...       ...        ...                ...  \n", "8758  0.002945 -0.623895  -0.623895                  0  \n", "8759  0.002945  1.147738   1.147738                  0  \n", "8760 -1.225914 -0.692035  -0.692035                  1  \n", "8761  1.231804 -1.237152  -1.237152                  0  \n", "8762  0.002945 -1.237152  -1.237152                  1  \n", "\n", "[8763 rows x 23 columns]"]}, "execution_count": 226, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 227, "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "cannot import name 'parse_version' from 'sklearn.utils' (c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\__init__.py)", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[1;32mIn[227], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mi<PERSON><PERSON>n\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mover_sampling\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m SMOTE\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01ms<PERSON>arn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodel_selection\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m train_test_split\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmetrics\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m accuracy_score, classification_report, confusion_matrix\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\imblearn\\__init__.py:52\u001b[0m\n\u001b[0;32m     48\u001b[0m     sys\u001b[38;5;241m.\u001b[39mstderr\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPartial import of imblearn during the build process.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     49\u001b[0m     \u001b[38;5;66;03m# We are not importing the rest of scikit-learn during the build\u001b[39;00m\n\u001b[0;32m     50\u001b[0m     \u001b[38;5;66;03m# process, as it may not be compiled yet\u001b[39;00m\n\u001b[0;32m     51\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m---> 52\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[0;32m     53\u001b[0m         combine,\n\u001b[0;32m     54\u001b[0m         ensemble,\n\u001b[0;32m     55\u001b[0m         exceptions,\n\u001b[0;32m     56\u001b[0m         metrics,\n\u001b[0;32m     57\u001b[0m         over_sampling,\n\u001b[0;32m     58\u001b[0m         pipeline,\n\u001b[0;32m     59\u001b[0m         tensorflow,\n\u001b[0;32m     60\u001b[0m         under_sampling,\n\u001b[0;32m     61\u001b[0m         utils,\n\u001b[0;32m     62\u001b[0m     )\n\u001b[0;32m     63\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_version\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m __version__\n\u001b[0;32m     64\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbase\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m FunctionSampler\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\imblearn\\combine\\__init__.py:5\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;124;03m\"\"\"The :mod:`imblearn.combine` provides methods which combine\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;124;03mover-sampling and under-sampling.\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m----> 5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_smote_enn\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m SMOTEENN\n\u001b[0;32m      6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_smote_tomek\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m SMOTETomek\n\u001b[0;32m      8\u001b[0m __all__ \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSMOTEENN\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSMOTETomek\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\imblearn\\combine\\_smote_enn.py:12\u001b[0m\n\u001b[0;32m      9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01ms<PERSON>arn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mba<PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m clone\n\u001b[0;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m check_X_y\n\u001b[1;32m---> 12\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m BaseSampler\n\u001b[0;32m     13\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mover_sampling\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m SMOTE\n\u001b[0;32m     14\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mover_sampling\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbase\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m BaseOverSampler\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\imblearn\\base.py:20\u001b[0m\n\u001b[0;32m     17\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01ms<PERSON>arn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _OneToOneFeatureMixin \u001b[38;5;28;01mas\u001b[39;00m OneToOneFeatureMixin\n\u001b[0;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpreprocessing\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m label_binarize\n\u001b[1;32m---> 20\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01ms<PERSON>arn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m parse_version\n\u001b[0;32m     21\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmulticlass\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m check_classification_targets\n\u001b[0;32m     23\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m check_sampling_strategy, check_target_type\n", "\u001b[1;31mImportError\u001b[0m: cannot import name 'parse_version' from 'sklearn.utils' (c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\__init__.py)"]}], "source": ["from imblearn.over_sampling import SMOTE\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "from xgboost import XGBClassifier\n", "import pandas as pd\n", "\n", "# Load dataset\n", "df=pd.read_csv(\"../datas/scaleddata.csv\")\n", "# Drop unnecessary column\n", "df = df.drop(columns=[\"Unnamed: 0\"])\n", "\n", "# Define target and features\n", "X = df.drop(columns=[\"Heart Attack Risk\"])\n", "y = df[\"Heart Attack Risk\"]\n", "\n", "# Top 12 selected features based on feature importance\n", "selected_features = [\n", "    \"Exercise Hours Per Week\", \"BMI\", \"Sedentary Hours Per Day\", \"Income\",\n", "    \"Triglycerides\", \"Cholesterol\", \"Age\", \"Heart Rate\", \"Systolic\", \n", "    \"Diastolic\", \"Stress Level\", \"Physical Activity Days Per Week\"\n", "]\n", "X_selected = X[selected_features]\n", "\n", "# Split dataset\n", "X_train, X_test, y_train, y_test = train_test_split(X_selected, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "# Apply SMOTE\n", "smote = SMOTE(sampling_strategy='auto', random_state=42)\n", "X_train_resampled, y_train_resampled = smote.fit_resample(X_train, y_train)\n", "\n", "# Train XGBoost Classifier\n", "xgb_model = XGBClassifier(use_label_encoder=False, eval_metric='logloss', random_state=42)\n", "xgb_model.fit(X_train_resampled, y_train_resampled)\n", "\n", "# Predictions\n", "y_pred = xgb_model.predict(X_test)\n", "\n", "# Evaluation\n", "accuracy = accuracy_score(y_test, y_pred)\n", "conf_matrix = confusion_matrix(y_test, y_pred)\n", "classification_rep = classification_report(y_test, y_pred)\n", "\n", "print(\"Accuracy:\", accuracy)\n", "print(\"Confusion Matrix:\\n\", conf_matrix)\n", "print(\"Classification Report:\\n\", classification_rep)\n"]}, {"cell_type": "code", "execution_count": 228, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.5253850541928123\n", "Confusion Matrix:\n", " [[614 511]\n", " [321 307]]\n", "Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.66      0.55      0.60      1125\n", "           1       0.38      0.49      0.42       628\n", "\n", "    accuracy                           0.53      1753\n", "   macro avg       0.52      0.52      0.51      1753\n", "weighted avg       0.56      0.53      0.53      1753\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\xgboost\\training.py:183: UserWarning: [18:45:25] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "from xgboost import XGBClassifier\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# Load dataset\n", "df=pd.read_csv(\"../datas/scaleddata.csv\")\n", "# Drop unnecessary column\n", "df = df.drop(columns=[\"Unnamed: 0\"])\n", "\n", "# Define target and features\n", "X = df.drop(columns=[\"Heart Attack Risk\"])\n", "y = df[\"Heart Attack Risk\"]\n", "\n", "# Top 12 selected features based on feature importance\n", "selected_features = [\n", "    \"Exercise Hours Per Week\", \"BMI\", \"Sedentary Hours Per Day\", \"Income\",\n", "    \"Triglycerides\", \"Cholesterol\", \"Age\", \"Heart Rate\", \"Systolic\", \n", "    \"Diastolic\", \"Stress Level\", \"Physical Activity Days Per Week\"\n", "]\n", "X_selected = X[selected_features]\n", "\n", "# Split dataset\n", "X_train, X_test, y_train, y_test = train_test_split(X_selected, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "# Compute class weights manually\n", "class_counts = np.bincount(y_train)\n", "class_weights = {i: sum(class_counts) / class_counts[i] for i in range(len(class_counts))}\n", "\n", "# Train XGBoost Classifier with class weights\n", "xgb_model = XGBClassifier(use_label_encoder=False, eval_metric='logloss', scale_pos_weight=class_weights[1], random_state=42)\n", "xgb_model.fit(X_train, y_train)\n", "\n", "# Predictions\n", "y_pred = xgb_model.predict(X_test)\n", "\n", "# Evaluation\n", "accuracy = accuracy_score(y_test, y_pred)\n", "conf_matrix = confusion_matrix(y_test, y_pred)\n", "classification_rep = classification_report(y_test, y_pred)\n", "\n", "print(\"Accuracy:\", accuracy)\n", "print(\"Confusion Matrix:\\n\", conf_matrix)\n", "print(\"Classification Report:\\n\", classification_rep)\n"]}, {"cell_type": "code", "execution_count": 230, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best Model Accuracy: 0.6394751853964632\n", "Confusion Matrix:\n", " [[1112   13]\n", " [ 619    9]]\n", "Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.64      0.99      0.78      1125\n", "           1       0.41      0.01      0.03       628\n", "\n", "    accuracy                           0.64      1753\n", "   macro avg       0.53      0.50      0.40      1753\n", "weighted avg       0.56      0.64      0.51      1753\n", "\n"]}], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import GridSearchCV, train_test_split\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "import pandas as pd\n", "\n", "# Load dataset\n", "df=pd.read_csv(\"../datas/scaleddata.csv\")\n", "# Drop unnecessary column\n", "df = df.drop(columns=[\"Unnamed: 0\"])\n", "\n", "# Define target and features\n", "X = df.drop(columns=[\"Heart Attack Risk\"])\n", "y = df[\"Heart Attack Risk\"]\n", "\n", "# Select the best features\n", "selected_features = [\n", "    \"Exercise Hours Per Week\", \"BMI\", \"Sedentary Hours Per Day\", \"Income\",\n", "    \"Triglycerides\", \"Cholesterol\", \"Age\", \"Heart Rate\", \"Systolic\", \n", "    \"Diastolic\", \"Stress Level\", \"Physical Activity Days Per Week\"\n", "]\n", "X_selected = X[selected_features]\n", "\n", "# Split dataset\n", "X_train, X_test, y_train, y_test = train_test_split(X_selected, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "# Initialize Random Forest with balanced class weights\n", "rf_model = RandomForestClassifier(class_weight=\"balanced\", random_state=42)\n", "\n", "# Define hyperparameters for tuning\n", "param_grid = {\n", "    \"n_estimators\": [100, 200, 300],\n", "    \"max_depth\": [10, 20, 30],\n", "    \"min_samples_split\": [2, 5, 10],\n", "    \"min_samples_leaf\": [1, 2, 4]\n", "}\n", "\n", "# Perform Grid Search for best parameters\n", "grid_search = GridSearchCV(rf_model, param_grid, cv=5, scoring=\"accuracy\", n_jobs=-1)\n", "grid_search.fit(X_train, y_train)\n", "\n", "# Get the best model\n", "best_rf_model = grid_search.best_estimator_\n", "\n", "# Make predictions\n", "y_pred_rf = best_rf_model.predict(X_test)\n", "\n", "# Evaluate the model\n", "accuracy_rf = accuracy_score(y_test, y_pred_rf)\n", "conf_matrix_rf = confusion_matrix(y_test, y_pred_rf)\n", "classification_rep_rf = classification_report(y_test, y_pred_rf)\n", "\n", "print(\"Best Model Accuracy:\", accuracy_rf)\n", "print(\"Confusion Matrix:\\n\", conf_matrix_rf)\n", "print(\"Classification Report:\\n\", classification_rep_rf)\n"]}, {"cell_type": "code", "execution_count": 231, "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-10 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-10 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-10 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-10 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-10 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-10 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-10 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-10 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-10 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-10 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-10 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-10 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-10 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-10 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-10 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-10 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-10 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-10 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-10 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-10 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-10 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-10 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-10 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-10 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-10 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-10 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-10 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-10 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-10 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-10 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-10 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-10 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-10 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-10 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-10 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-10 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-10 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-10 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-10 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-10 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-10 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-10 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-10 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-10\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>SVC()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-10\" type=\"checkbox\" checked><label for=\"sk-estimator-id-10\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>SVC</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.6/modules/generated/sklearn.svm.SVC.html\">?<span>Documentation for SVC</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\"><pre>SVC()</pre></div> </div></div></div></div>"], "text/plain": ["SVC()"]}, "execution_count": 231, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.svm import SVC\n", "S=SVC()\n", "S.fit(x_train,y_train)"]}, {"cell_type": "code", "execution_count": 232, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.6411865373645179"]}, "execution_count": 232, "metadata": {}, "output_type": "execute_result"}], "source": ["pred=S.predict(x_test)\n", "from sklearn.metrics import accuracy_score,confusion_matrix\n", "acc=accuracy_score(y_test,pred)\n", "conf=confusion_matrix(y_test,pred)\n", "conf\n", "acc"]}, {"cell_type": "code", "execution_count": 233, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1124,    1],\n", "       [ 628,    0]], dtype=int64)"]}, "execution_count": 233, "metadata": {}, "output_type": "execute_result"}], "source": ["conf"]}, {"cell_type": "code", "execution_count": 234, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Heart Attack Risk\n", "1    3139\n", "0    3139\n", "Name: count, dtype: int64\n"]}], "source": ["import pandas as pd\n", "from sklearn.utils import shuffle\n", "\n", "# Assuming 'df' is your dataset and the target column is named 'Heart Attack Risk'\n", "majority_class = df[df['Heart Attack Risk'] == 0]\n", "minority_class = df[df['Heart Attack Risk'] == 1]\n", "\n", "# Downsample the majority class to match the minority class size\n", "majority_class_downsampled = majority_class.sample(n=len(minority_class), random_state=42)\n", "\n", "# Combine the balanced dataset\n", "balanced_df = pd.concat([majority_class_downsampled, minority_class])\n", "\n", "# Shuffle the dataset\n", "balanced_df = shuffle(balanced_df, random_state=42)\n", "\n", "# Reset index\n", "balanced_df = balanced_df.reset_index(drop=True)\n", "\n", "# Display the class distribution\n", "print(balanced_df['Heart Attack Risk'].value_counts())\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'y' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodel_selection\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m train_test_split\n\u001b[1;32m----> 2\u001b[0m x_train,x_test,y_train,y_test\u001b[38;5;241m=\u001b[39mtrain_test_split(df,y,test_size\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.20\u001b[39m)\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mneighbors\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m KNeighborsClassifier\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mensemble\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m RandomForestClassifier\n", "\u001b[1;31mNameError\u001b[0m: name 'y' is not defined"]}], "source": ["\n", "from sklearn.model_selection import train_test_split\n", "x_train,x_test,y_train,y_test=train_test_split(df,y,test_size=0.20)\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.ensemble import RandomForestClassifier\n", "model=RandomForestClassifier()\n", "model.fit(x_train,y_train)\n", "pred=model.predict(x_test)\n", "from sklearn.metrics import accuracy_score,confusion_matrix\n", "acc=accuracy_score(y_test,pred)\n", "conf=confusion_matrix(y_test,pred)\n", "conf\n", "acc"]}, {"cell_type": "code", "execution_count": 242, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[346, 271],\n", "       [363, 276]], dtype=int64)"]}, "execution_count": 242, "metadata": {}, "output_type": "execute_result"}], "source": ["conf"]}, {"cell_type": "code", "execution_count": 239, "metadata": {}, "outputs": [{"data": {"text/plain": ["Heart Attack Risk\n", "0    5624\n", "1    3139\n", "Name: count, dtype: int64"]}, "execution_count": 239, "metadata": {}, "output_type": "execute_result"}], "source": ["y.value_counts()"]}, {"cell_type": "code", "execution_count": 240, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Heart Attack Risk\n", "1    3139\n", "0    3139\n", "Name: count, dtype: int64\n"]}], "source": ["print(balanced_df['Heart Attack Risk'].value_counts())"]}, {"cell_type": "code", "execution_count": 246, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Cholesterol</th>\n", "      <th>Heart Rate</th>\n", "      <th>Diabetes</th>\n", "      <th>Family History</th>\n", "      <th>Smoking</th>\n", "      <th>Obesity</th>\n", "      <th>Alcohol Consumption</th>\n", "      <th>Exercise Hours Per Week</th>\n", "      <th>Previous Heart Problems</th>\n", "      <th>...</th>\n", "      <th>Sedentary Hours Per Day</th>\n", "      <th>Income</th>\n", "      <th>BMI</th>\n", "      <th>Triglycerides</th>\n", "      <th>Physical Activity Days Per Week</th>\n", "      <th>Sleep Hours Per Day</th>\n", "      <th>Sex</th>\n", "      <th>Diet</th>\n", "      <th>Systolic</th>\n", "      <th>Diast<PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-0.268632</td>\n", "      <td>-0.864190</td>\n", "      <td>1.166841</td>\n", "      <td>-1.369651</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.692466</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>-1.288899</td>\n", "      <td>-1.709460</td>\n", "      <td>0.541628</td>\n", "      <td>0.175757</td>\n", "      <td>-0.652633</td>\n", "      <td>0.994032</td>\n", "      <td>0.658765</td>\n", "      <td>0.002945</td>\n", "      <td>0.330061</td>\n", "      <td>0.330061</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-0.598070</td>\n", "      <td>-1.470185</td>\n", "      <td>-0.925639</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>-1.219867</td>\n", "      <td>1.490591</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>0.692114</td>\n", "      <td>1.538822</td>\n", "      <td>1.316184</td>\n", "      <td>-1.728279</td>\n", "      <td>0.223577</td>\n", "      <td>-1.017677</td>\n", "      <td>0.658765</td>\n", "      <td>0.002945</td>\n", "      <td>-1.237152</td>\n", "      <td>-1.237152</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-0.739258</td>\n", "      <td>-0.320030</td>\n", "      <td>-0.001055</td>\n", "      <td>-1.369651</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>0.026774</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>-1.333100</td>\n", "      <td>-0.958801</td>\n", "      <td>1.171765</td>\n", "      <td>1.074139</td>\n", "      <td>-0.214528</td>\n", "      <td>-0.514750</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>-0.215057</td>\n", "      <td>-0.215057</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-0.974571</td>\n", "      <td>0.335435</td>\n", "      <td>1.410153</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>0.170422</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>0.942217</td>\n", "      <td>0.753486</td>\n", "      <td>0.581769</td>\n", "      <td>1.413826</td>\n", "      <td>0.223577</td>\n", "      <td>0.491104</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>1.556576</td>\n", "      <td>1.556576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.060806</td>\n", "      <td>-1.198106</td>\n", "      <td>-0.585003</td>\n", "      <td>-1.369651</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>-1.219867</td>\n", "      <td>0.252590</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.968615</td>\n", "      <td>-0.002894</td>\n", "      <td>-0.361251</td>\n", "      <td>-1.281322</td>\n", "      <td>-1.528843</td>\n", "      <td>-0.011823</td>\n", "      <td>0.658765</td>\n", "      <td>0.002945</td>\n", "      <td>1.488437</td>\n", "      <td>1.488437</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6273</th>\n", "      <td>1.284433</td>\n", "      <td>0.793023</td>\n", "      <td>-0.876977</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-1.521219</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>0.549356</td>\n", "      <td>0.920470</td>\n", "      <td>1.513102</td>\n", "      <td>0.859600</td>\n", "      <td>-1.090738</td>\n", "      <td>0.994032</td>\n", "      <td>0.658765</td>\n", "      <td>0.002945</td>\n", "      <td>-0.760174</td>\n", "      <td>-0.760174</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6274</th>\n", "      <td>1.378559</td>\n", "      <td>-1.705163</td>\n", "      <td>0.972192</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.288231</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>-0.695351</td>\n", "      <td>-1.556352</td>\n", "      <td>-1.143267</td>\n", "      <td>1.212696</td>\n", "      <td>1.099787</td>\n", "      <td>-1.520604</td>\n", "      <td>-1.517992</td>\n", "      <td>1.231804</td>\n", "      <td>1.352157</td>\n", "      <td>1.352157</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6275</th>\n", "      <td>-0.974571</td>\n", "      <td>-0.282928</td>\n", "      <td>1.702127</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.804565</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>1.123653</td>\n", "      <td>0.787307</td>\n", "      <td>-0.229915</td>\n", "      <td>0.028261</td>\n", "      <td>-1.528843</td>\n", "      <td>1.496959</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>1.011458</td>\n", "      <td>1.011458</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6276</th>\n", "      <td>0.813808</td>\n", "      <td>0.088089</td>\n", "      <td>1.020854</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>-1.067604</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.989186</td>\n", "      <td>-0.653975</td>\n", "      <td>-1.219857</td>\n", "      <td>-0.888000</td>\n", "      <td>-0.652633</td>\n", "      <td>-0.514750</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>1.420297</td>\n", "      <td>1.420297</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6277</th>\n", "      <td>1.707997</td>\n", "      <td>1.188775</td>\n", "      <td>-0.390354</td>\n", "      <td>-1.369651</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.103013</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>1.052950</td>\n", "      <td>-1.596317</td>\n", "      <td>0.443531</td>\n", "      <td>-0.168400</td>\n", "      <td>1.099787</td>\n", "      <td>-0.011823</td>\n", "      <td>-1.517992</td>\n", "      <td>1.231804</td>\n", "      <td>-0.760174</td>\n", "      <td>-0.760174</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6278 rows × 22 columns</p>\n", "</div>"], "text/plain": ["           Age  Cholesterol  Heart Rate  Diabetes  Family History   Smoking  \\\n", "0    -0.268632    -0.864190    1.166841 -1.369651       -0.986061  0.339157   \n", "1    -0.598070    -1.470185   -0.925639  0.730113        1.014136  0.339157   \n", "2    -0.739258    -0.320030   -0.001055 -1.369651        1.014136  0.339157   \n", "3    -0.974571     0.335435    1.410153  0.730113       -0.986061  0.339157   \n", "4     0.060806    -1.198106   -0.585003 -1.369651       -0.986061  0.339157   \n", "...        ...          ...         ...       ...             ...       ...   \n", "6273  1.284433     0.793023   -0.876977  0.730113        1.014136  0.339157   \n", "6274  1.378559    -1.705163    0.972192  0.730113        1.014136  0.339157   \n", "6275 -0.974571    -0.282928    1.702127  0.730113        1.014136  0.339157   \n", "6276  0.813808     0.088089    1.020854  0.730113        1.014136  0.339157   \n", "6277  1.707997     1.188775   -0.390354 -1.369651       -0.986061  0.339157   \n", "\n", "       Obesity  Alcohol Consumption  Exercise Hours Per Week  \\\n", "0    -1.002857             0.819762                -0.692466   \n", "1     0.997151            -1.219867                 1.490591   \n", "2     0.997151             0.819762                 0.026774   \n", "3     0.997151             0.819762                 0.170422   \n", "4     0.997151            -1.219867                 0.252590   \n", "...        ...                  ...                      ...   \n", "6273 -1.002857             0.819762                -1.521219   \n", "6274 -1.002857             0.819762                -0.288231   \n", "6275 -1.002857             0.819762                -0.804565   \n", "6276 -1.002857            -1.219867                -1.067604   \n", "6277 -1.002857             0.819762                -0.103013   \n", "\n", "      Previous Heart Problems  ...  Sedentary Hours Per Day    Income  \\\n", "0                   -0.991704  ...                -1.288899 -1.709460   \n", "1                   -0.991704  ...                 0.692114  1.538822   \n", "2                   -0.991704  ...                -1.333100 -0.958801   \n", "3                   -0.991704  ...                 0.942217  0.753486   \n", "4                    1.008365  ...                 0.968615 -0.002894   \n", "...                       ...  ...                      ...       ...   \n", "6273                -0.991704  ...                 0.549356  0.920470   \n", "6274                 1.008365  ...                -0.695351 -1.556352   \n", "6275                 1.008365  ...                 1.123653  0.787307   \n", "6276                 1.008365  ...                 0.989186 -0.653975   \n", "6277                -0.991704  ...                 1.052950 -1.596317   \n", "\n", "           BMI  Triglycerides  Physical Activity Days Per Week  \\\n", "0     0.541628       0.175757                        -0.652633   \n", "1     1.316184      -1.728279                         0.223577   \n", "2     1.171765       1.074139                        -0.214528   \n", "3     0.581769       1.413826                         0.223577   \n", "4    -0.361251      -1.281322                        -1.528843   \n", "...        ...            ...                              ...   \n", "6273  1.513102       0.859600                        -1.090738   \n", "6274 -1.143267       1.212696                         1.099787   \n", "6275 -0.229915       0.028261                        -1.528843   \n", "6276 -1.219857      -0.888000                        -0.652633   \n", "6277  0.443531      -0.168400                         1.099787   \n", "\n", "      Sleep Hours Per Day       Sex      Diet  Systolic  Diastolic  \n", "0                0.994032  0.658765  0.002945  0.330061   0.330061  \n", "1               -1.017677  0.658765  0.002945 -1.237152  -1.237152  \n", "2               -0.514750  0.658765  1.231804 -0.215057  -0.215057  \n", "3                0.491104  0.658765 -1.225914  1.556576   1.556576  \n", "4               -0.011823  0.658765  0.002945  1.488437   1.488437  \n", "...                   ...       ...       ...       ...        ...  \n", "6273             0.994032  0.658765  0.002945 -0.760174  -0.760174  \n", "6274            -1.520604 -1.517992  1.231804  1.352157   1.352157  \n", "6275             1.496959  0.658765 -1.225914  1.011458   1.011458  \n", "6276            -0.514750  0.658765  1.231804  1.420297   1.420297  \n", "6277            -0.011823 -1.517992  1.231804 -0.760174  -0.760174  \n", "\n", "[6278 rows x 22 columns]"]}, "execution_count": 246, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 247, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimal number of features: 20\n", "Selected Features: ['Age', 'Cholesterol', 'Heart Rate', 'Family History', 'Obesity', 'Alcohol Consumption', 'Exercise Hours Per Week', 'Previous Heart Problems', 'Medication Use', 'Stress Level', 'Sedentary Hours Per Day', 'Income', 'BMI', 'Triglycerides', 'Physical Activity Days Per Week', 'Sleep Hours Per Day', 'Sex', 'Diet', 'Systolic', 'Diastolic']\n", "Model Accuracy with Selected Features: 0.4721337579617834\n"]}], "source": ["import pandas as pd\n", "from sklearn.feature_selection import RFECV\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split, StratifiedKFold\n", "from sklearn.metrics import accuracy_score\n", "\n", "# Assuming 'df' is your dataset and 'y' is the target variable\n", "X = df  # Feature matrix\n", "y = y   # Target variable\n", "\n", "# Split data into training and testing sets\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "# Define the model for feature selection\n", "model = RandomForestClassifier(random_state=42)\n", "\n", "# Perform Recursive Feature Elimination with Cross-Validation\n", "rfecv = RFECV(estimator=model, step=1, cv=StratifiedKFold(5), scoring='accuracy')\n", "\n", "# Fit the RFECV model\n", "rfecv.fit(X_train, y_train)\n", "\n", "# Get the selected features\n", "selected_features = X.columns[rfecv.support_]\n", "\n", "print(\"Optimal number of features:\", rfecv.n_features_)\n", "print(\"Selected Features:\", list(selected_features))\n", "\n", "# Train model with selected features and evaluate performance\n", "model.fit(X_train[selected_features], y_train)\n", "y_pred = model.predict(X_test[selected_features])\n", "accuracy = accuracy_score(y_test, y_pred)\n", "\n", "print(\"Model Accuracy with Selected Features:\", accuracy)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Heart Attack Risk\n", "1    3139\n", "0    1624\n", "Name: count, dtype: int64\n"]}], "source": ["\n", "import pandas as pd\n", "\n", "# Sample DataFrame (Replace with actual dataset)\n", "\n", "\n", "# Filtering out all rows where Target == 0\n", "df_class_0 = df[df['Heart Attack Risk'] == 0]\n", "df_class_1 = df[df['Heart Attack Risk'] != 0]\n", "\n", "# Randomly dropping 3000 rows from class 0\n", "df_class_0_sampled = df_class_0.sample(n=len(df_class_0) - 4000, random_state=42)\n", "\n", "# Concatenating the remaining rows\n", "df_balanced = pd.concat([df_class_0_sampled, df_class_1])\n", "\n", "# Shuffling the dataset\n", "df_balanced = df_balanced.sample(frac=1, random_state=42).reset_index(drop=True)\n", "\n", "print(df_balanced['Heart Attack Risk'].value_counts())  # Check class distribution\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df=df_balanced\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.6002098635886673"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "from sklearn.model_selection import train_test_split\n", "x_train,x_test,y_train,y_test=train_test_split(df,y,test_size=0.20)\n", "from sklearn.ensemble import RandomForestClassifier\n", "model=RandomForestClassifier(class_weight={0: 1, 1: 15})\n", "model.fit(x_train,y_train)\n", "pred=model.predict(x_test)\n", "from sklearn.metrics import accuracy_score,confusion_matrix\n", "acc=accuracy_score(y_test,pred)\n", "conf=confusion_matrix(y_test,pred)\n", "conf\n", "acc"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 57, 264],\n", "       [117, 515]], dtype=int64)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["conf"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "when `importance_getter=='auto'`, the underlying estimator KNeighborsClassifier should have `coef_` or `feature_importances_` attribute. Either pass a fitted estimator to feature selector or call fit before calling transform.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m_RemoteTraceback\u001b[0m                          Traceback (most recent call last)", "\u001b[1;31m_RemoteTraceback\u001b[0m: \n\"\"\"\nTraceback (most recent call last):\n  File \"c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\joblib\\externals\\loky\\process_executor.py\", line 428, in _process_worker\n    r = call_item()\n        ^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\joblib\\externals\\loky\\process_executor.py\", line 275, in __call__\n    return self.fn(*self.args, **self.kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\joblib\\_parallel_backends.py\", line 620, in __call__\n    return self.func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\joblib\\parallel.py\", line 288, in __call__\n    return [func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\joblib\\parallel.py\", line 288, in <listcomp>\n    return [func(*args, **kwargs)\n            ^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\parallel.py\", line 139, in __call__\n    return self.function(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\feature_selection\\_rfe.py\", line 52, in _rfe_single_fit\n    rfe._fit(\n  File \"c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\feature_selection\\_rfe.py\", line 335, in _fit\n    importances = _get_feature_importances(\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\feature_selection\\_base.py\", line 234, in _get_feature_importances\n    raise ValueError(\nValueError: when `importance_getter=='auto'`, the underlying estimator KNeighborsClassifier should have `coef_` or `feature_importances_` attribute. Either pass a fitted estimator to feature selector or call fit before calling transform.\n\"\"\"", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[43], line 24\u001b[0m\n\u001b[0;32m     21\u001b[0m selector \u001b[38;5;241m=\u001b[39m RFECV(estimator\u001b[38;5;241m=\u001b[39mknn, step\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m, cv\u001b[38;5;241m=\u001b[39mcv, scoring\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124maccuracy\u001b[39m\u001b[38;5;124m'\u001b[39m, n_jobs\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[0;32m     23\u001b[0m \u001b[38;5;66;03m# Fit RFECV to find the best features\u001b[39;00m\n\u001b[1;32m---> 24\u001b[0m selector\u001b[38;5;241m.\u001b[39mfit(x_train, y_train)\n\u001b[0;32m     26\u001b[0m \u001b[38;5;66;03m# Transform dataset to keep only selected features\u001b[39;00m\n\u001b[0;32m     27\u001b[0m x_train_selected \u001b[38;5;241m=\u001b[39m selector\u001b[38;5;241m.\u001b[39mtransform(x_train)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\validation.py:63\u001b[0m, in \u001b[0;36m_deprecate_positional_args.<locals>._inner_deprecate_positional_args.<locals>.inner_f\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m     61\u001b[0m extra_args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlen\u001b[39m(args) \u001b[38;5;241m-\u001b[39m \u001b[38;5;28mlen\u001b[39m(all_args)\n\u001b[0;32m     62\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m extra_args \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m---> 63\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m f(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m     65\u001b[0m \u001b[38;5;66;03m# extra_args > 0\u001b[39;00m\n\u001b[0;32m     66\u001b[0m args_msg \u001b[38;5;241m=\u001b[39m [\n\u001b[0;32m     67\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m=\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(name, arg)\n\u001b[0;32m     68\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m name, arg \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(kwonly_args[:extra_args], args[\u001b[38;5;241m-\u001b[39mextra_args:])\n\u001b[0;32m     69\u001b[0m ]\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\base.py:1389\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1382\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1384\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1385\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1386\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1387\u001b[0m     )\n\u001b[0;32m   1388\u001b[0m ):\n\u001b[1;32m-> 1389\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m fit_method(estimator, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\feature_selection\\_rfe.py:873\u001b[0m, in \u001b[0;36mRFECV.fit\u001b[1;34m(self, X, y, groups, **params)\u001b[0m\n\u001b[0;32m    870\u001b[0m     parallel \u001b[38;5;241m=\u001b[39m Parallel(n_jobs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mn_jobs)\n\u001b[0;32m    871\u001b[0m     func \u001b[38;5;241m=\u001b[39m delayed(_rfe_single_fit)\n\u001b[1;32m--> 873\u001b[0m scores_features \u001b[38;5;241m=\u001b[39m parallel(\n\u001b[0;32m    874\u001b[0m     func(clone(rfe), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mestimator, X, y, train, test, scorer, routed_params)\n\u001b[0;32m    875\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m train, test \u001b[38;5;129;01min\u001b[39;00m cv\u001b[38;5;241m.\u001b[39msplit(X, y, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mrouted_params\u001b[38;5;241m.\u001b[39msplitter\u001b[38;5;241m.\u001b[39msplit)\n\u001b[0;32m    876\u001b[0m )\n\u001b[0;32m    877\u001b[0m scores, step_n_features \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mzip\u001b[39m(\u001b[38;5;241m*\u001b[39mscores_features)\n\u001b[0;32m    879\u001b[0m step_n_features_rev \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(step_n_features[\u001b[38;5;241m0\u001b[39m])[::\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\parallel.py:77\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m     72\u001b[0m config \u001b[38;5;241m=\u001b[39m get_config()\n\u001b[0;32m     73\u001b[0m iterable_with_config \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m     74\u001b[0m     (_with_config(delayed_func, config), args, kwargs)\n\u001b[0;32m     75\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m delayed_func, args, kwargs \u001b[38;5;129;01min\u001b[39;00m iterable\n\u001b[0;32m     76\u001b[0m )\n\u001b[1;32m---> 77\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__call__\u001b[39m(iterable_with_config)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\joblib\\parallel.py:1098\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m   1095\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_iterating \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[0;32m   1097\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend\u001b[38;5;241m.\u001b[39mretrieval_context():\n\u001b[1;32m-> 1098\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mretrieve()\n\u001b[0;32m   1099\u001b[0m \u001b[38;5;66;03m# Make sure that we get a last message telling us we are done\u001b[39;00m\n\u001b[0;32m   1100\u001b[0m elapsed_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime() \u001b[38;5;241m-\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_start_time\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\joblib\\parallel.py:975\u001b[0m, in \u001b[0;36mParallel.retrieve\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    973\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m    974\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124msupports_timeout\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;28;01mFalse\u001b[39;00m):\n\u001b[1;32m--> 975\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_output\u001b[38;5;241m.\u001b[39mextend(job\u001b[38;5;241m.\u001b[39mget(timeout\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtimeout))\n\u001b[0;32m    976\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    977\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_output\u001b[38;5;241m.\u001b[39mextend(job\u001b[38;5;241m.\u001b[39mget())\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\joblib\\_parallel_backends.py:567\u001b[0m, in \u001b[0;36mLokyBackend.wrap_future_result\u001b[1;34m(future, timeout)\u001b[0m\n\u001b[0;32m    564\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Wrapper for Future.result to implement the same behaviour as\u001b[39;00m\n\u001b[0;32m    565\u001b[0m \u001b[38;5;124;03mAsyncResults.get from multiprocessing.\"\"\"\u001b[39;00m\n\u001b[0;32m    566\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 567\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m future\u001b[38;5;241m.\u001b[39mresult(timeout\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[0;32m    568\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m CfTimeoutError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    569\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTimeoutError\u001b[39;00m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\futures\\_base.py:456\u001b[0m, in \u001b[0;36mFuture.result\u001b[1;34m(self, timeout)\u001b[0m\n\u001b[0;32m    454\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m CancelledError()\n\u001b[0;32m    455\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_state \u001b[38;5;241m==\u001b[39m FINISHED:\n\u001b[1;32m--> 456\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__get_result()\n\u001b[0;32m    457\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    458\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTimeoutError\u001b[39;00m()\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\concurrent\\futures\\_base.py:401\u001b[0m, in \u001b[0;36mFuture.__get_result\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    399\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception:\n\u001b[0;32m    400\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 401\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception\n\u001b[0;32m    402\u001b[0m     \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[0;32m    403\u001b[0m         \u001b[38;5;66;03m# Break a reference cycle with the exception in self._exception\u001b[39;00m\n\u001b[0;32m    404\u001b[0m         \u001b[38;5;28mself\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n", "\u001b[1;31mValueError\u001b[0m: when `importance_getter=='auto'`, the underlying estimator KNeighborsClassifier should have `coef_` or `feature_importances_` attribute. Either pass a fitted estimator to feature selector or call fit before calling transform."]}], "source": ["from sklearn.model_selection import train_test_split, StratifiedKFold\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.feature_selection import RFECV\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import accuracy_score, make_scorer\n", "import numpy as np\n", "\n", "# Splitting the dataset\n", "x_train, x_test, y_train, y_test = train_test_split(df, y, test_size=0.20, random_state=42)\n", "\n", "# Define KNN model\n", "knn = KNeighborsClassifier(n_neighbors=5)\n", "\n", "\n", "# Define a custom scoring function (accuracy-based)\n", "scoring_function = make_scorer(accuracy_score)\n", "\n", "# RFECV with a custom scoring function\n", "cv = StratifiedKFold(n_splits=5)  # Ensure balanced class splits\n", "selector = RFECV(estimator=knn, step=1, cv=cv, scoring='accuracy', n_jobs=-1)\n", "\n", "# Fit RFECV to find the best features\n", "selector.fit(x_train, y_train)\n", "\n", "# Transform dataset to keep only selected features\n", "x_train_selected = selector.transform(x_train)\n", "x_test_selected = selector.transform(x_test)\n", "\n", "# Train model on the selected features\n", "knn.fit(x_train_selected, y_train)\n", "\n", "# Predictions\n", "pred = knn.predict(x_test_selected)\n", "\n", "# Evaluate performance\n", "acc = accuracy_score(y_test, pred)\n", "\n", "print(\"Optimal number of features:\", selector.n_features_)\n", "print(\"Selected features:\", df.columns[selector.support_])\n", "print(\"Accuracy:\", acc)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Cholesterol</th>\n", "      <th>Heart Rate</th>\n", "      <th>Diabetes</th>\n", "      <th>Family History</th>\n", "      <th>Smoking</th>\n", "      <th>Obesity</th>\n", "      <th>Alcohol Consumption</th>\n", "      <th>Exercise Hours Per Week</th>\n", "      <th>Previous Heart Problems</th>\n", "      <th>...</th>\n", "      <th>Sedentary Hours Per Day</th>\n", "      <th>Income</th>\n", "      <th>BMI</th>\n", "      <th>Triglycerides</th>\n", "      <th>Physical Activity Days Per Week</th>\n", "      <th>Sleep Hours Per Day</th>\n", "      <th>Sex</th>\n", "      <th>Diet</th>\n", "      <th>Systolic</th>\n", "      <th>Diast<PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.860870</td>\n", "      <td>0.112824</td>\n", "      <td>1.458815</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>-0.368260</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.513364</td>\n", "      <td>0.511835</td>\n", "      <td>-1.095849</td>\n", "      <td>0.957931</td>\n", "      <td>-1.090738</td>\n", "      <td>0.491104</td>\n", "      <td>0.658765</td>\n", "      <td>0.002945</td>\n", "      <td>1.011458</td>\n", "      <td>1.011458</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.154931</td>\n", "      <td>0.409638</td>\n", "      <td>1.507477</td>\n", "      <td>-1.369651</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>1.135042</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>1.329090</td>\n", "      <td>-1.471072</td>\n", "      <td>1.230150</td>\n", "      <td>1.422765</td>\n", "      <td>-1.528843</td>\n", "      <td>1.496959</td>\n", "      <td>0.658765</td>\n", "      <td>0.002945</td>\n", "      <td>1.284017</td>\n", "      <td>1.284017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-1.162822</td>\n", "      <td>-0.926026</td>\n", "      <td>0.242257</td>\n", "      <td>-1.369651</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>-1.219867</td>\n", "      <td>0.081703</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>1.428320</td>\n", "      <td>1.274110</td>\n", "      <td>1.546429</td>\n", "      <td>1.033913</td>\n", "      <td>-1.528843</td>\n", "      <td>-0.011823</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>-0.555755</td>\n", "      <td>-0.555755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-1.586385</td>\n", "      <td>-0.567375</td>\n", "      <td>-0.049717</td>\n", "      <td>-1.369651</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>0.221511</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>1.401116</td>\n", "      <td>1.406379</td>\n", "      <td>0.248050</td>\n", "      <td>1.257391</td>\n", "      <td>-1.528843</td>\n", "      <td>-1.520604</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>-1.645991</td>\n", "      <td>-1.645991</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.237371</td>\n", "      <td>0.100457</td>\n", "      <td>1.312828</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>-1.219867</td>\n", "      <td>-0.187643</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>-0.302274</td>\n", "      <td>-1.277975</td>\n", "      <td>0.113666</td>\n", "      <td>0.300904</td>\n", "      <td>-0.652633</td>\n", "      <td>-0.011823</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>-1.100873</td>\n", "      <td>-1.100873</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4758</th>\n", "      <td>1.331496</td>\n", "      <td>-1.705163</td>\n", "      <td>-1.120289</td>\n", "      <td>0.730113</td>\n", "      <td>1.014136</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.589689</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>-1.304676</td>\n", "      <td>-0.777023</td>\n", "      <td>-0.923164</td>\n", "      <td>-0.172870</td>\n", "      <td>-0.214528</td>\n", "      <td>-0.514750</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>1.420297</td>\n", "      <td>1.420297</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4759</th>\n", "      <td>0.578495</td>\n", "      <td>0.570412</td>\n", "      <td>0.923529</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>1.254404</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>-0.411085</td>\n", "      <td>0.012533</td>\n", "      <td>0.917905</td>\n", "      <td>0.466278</td>\n", "      <td>-0.214528</td>\n", "      <td>-0.011823</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>-1.714130</td>\n", "      <td>-1.714130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4760</th>\n", "      <td>-1.398135</td>\n", "      <td>0.953797</td>\n", "      <td>-0.585003</td>\n", "      <td>0.730113</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>1.010685</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.258669</td>\n", "      <td>0.004193</td>\n", "      <td>-0.929491</td>\n", "      <td>-0.932696</td>\n", "      <td>0.661682</td>\n", "      <td>0.491104</td>\n", "      <td>0.658765</td>\n", "      <td>-1.225914</td>\n", "      <td>1.420297</td>\n", "      <td>1.420297</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4761</th>\n", "      <td>1.378559</td>\n", "      <td>-0.258194</td>\n", "      <td>-1.558250</td>\n", "      <td>-1.369651</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>0.997151</td>\n", "      <td>0.819762</td>\n", "      <td>-1.215313</td>\n", "      <td>1.008365</td>\n", "      <td>...</td>\n", "      <td>0.066304</td>\n", "      <td>-0.223744</td>\n", "      <td>-0.310500</td>\n", "      <td>1.257391</td>\n", "      <td>-0.214528</td>\n", "      <td>-0.011823</td>\n", "      <td>0.658765</td>\n", "      <td>1.231804</td>\n", "      <td>-1.645991</td>\n", "      <td>-1.645991</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4762</th>\n", "      <td>1.707997</td>\n", "      <td>1.188775</td>\n", "      <td>-0.390354</td>\n", "      <td>-1.369651</td>\n", "      <td>-0.986061</td>\n", "      <td>0.339157</td>\n", "      <td>-1.002857</td>\n", "      <td>0.819762</td>\n", "      <td>-0.103013</td>\n", "      <td>-0.991704</td>\n", "      <td>...</td>\n", "      <td>1.052950</td>\n", "      <td>-1.596317</td>\n", "      <td>0.443531</td>\n", "      <td>-0.168400</td>\n", "      <td>1.099787</td>\n", "      <td>-0.011823</td>\n", "      <td>-1.517992</td>\n", "      <td>1.231804</td>\n", "      <td>-0.760174</td>\n", "      <td>-0.760174</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4763 rows × 22 columns</p>\n", "</div>"], "text/plain": ["           Age  Cholesterol  Heart Rate  Diabetes  Family History   Smoking  \\\n", "0     0.860870     0.112824    1.458815  0.730113       -0.986061  0.339157   \n", "1     0.154931     0.409638    1.507477 -1.369651       -0.986061  0.339157   \n", "2    -1.162822    -0.926026    0.242257 -1.369651        1.014136  0.339157   \n", "3    -1.586385    -0.567375   -0.049717 -1.369651       -0.986061  0.339157   \n", "4     1.237371     0.100457    1.312828  0.730113        1.014136  0.339157   \n", "...        ...          ...         ...       ...             ...       ...   \n", "4758  1.331496    -1.705163   -1.120289  0.730113        1.014136  0.339157   \n", "4759  0.578495     0.570412    0.923529  0.730113       -0.986061  0.339157   \n", "4760 -1.398135     0.953797   -0.585003  0.730113       -0.986061  0.339157   \n", "4761  1.378559    -0.258194   -1.558250 -1.369651       -0.986061  0.339157   \n", "4762  1.707997     1.188775   -0.390354 -1.369651       -0.986061  0.339157   \n", "\n", "       Obesity  Alcohol Consumption  Exercise Hours Per Week  \\\n", "0     0.997151             0.819762                -0.368260   \n", "1    -1.002857             0.819762                 1.135042   \n", "2    -1.002857            -1.219867                 0.081703   \n", "3     0.997151             0.819762                 0.221511   \n", "4     0.997151            -1.219867                -0.187643   \n", "...        ...                  ...                      ...   \n", "4758 -1.002857             0.819762                -0.589689   \n", "4759 -1.002857             0.819762                 1.254404   \n", "4760 -1.002857             0.819762                 1.010685   \n", "4761  0.997151             0.819762                -1.215313   \n", "4762 -1.002857             0.819762                -0.103013   \n", "\n", "      Previous Heart Problems  ...  Sedentary Hours Per Day    Income  \\\n", "0                    1.008365  ...                 0.513364  0.511835   \n", "1                    1.008365  ...                 1.329090 -1.471072   \n", "2                   -0.991704  ...                 1.428320  1.274110   \n", "3                   -0.991704  ...                 1.401116  1.406379   \n", "4                    1.008365  ...                -0.302274 -1.277975   \n", "...                       ...  ...                      ...       ...   \n", "4758                 1.008365  ...                -1.304676 -0.777023   \n", "4759                -0.991704  ...                -0.411085  0.012533   \n", "4760                 1.008365  ...                 0.258669  0.004193   \n", "4761                 1.008365  ...                 0.066304 -0.223744   \n", "4762                -0.991704  ...                 1.052950 -1.596317   \n", "\n", "           BMI  Triglycerides  Physical Activity Days Per Week  \\\n", "0    -1.095849       0.957931                        -1.090738   \n", "1     1.230150       1.422765                        -1.528843   \n", "2     1.546429       1.033913                        -1.528843   \n", "3     0.248050       1.257391                        -1.528843   \n", "4     0.113666       0.300904                        -0.652633   \n", "...        ...            ...                              ...   \n", "4758 -0.923164      -0.172870                        -0.214528   \n", "4759  0.917905       0.466278                        -0.214528   \n", "4760 -0.929491      -0.932696                         0.661682   \n", "4761 -0.310500       1.257391                        -0.214528   \n", "4762  0.443531      -0.168400                         1.099787   \n", "\n", "      Sleep Hours Per Day       Sex      Diet  Systolic  Diastolic  \n", "0                0.491104  0.658765  0.002945  1.011458   1.011458  \n", "1                1.496959  0.658765  0.002945  1.284017   1.284017  \n", "2               -0.011823  0.658765 -1.225914 -0.555755  -0.555755  \n", "3               -1.520604  0.658765  1.231804 -1.645991  -1.645991  \n", "4               -0.011823  0.658765 -1.225914 -1.100873  -1.100873  \n", "...                   ...       ...       ...       ...        ...  \n", "4758            -0.514750  0.658765 -1.225914  1.420297   1.420297  \n", "4759            -0.011823  0.658765  1.231804 -1.714130  -1.714130  \n", "4760             0.491104  0.658765 -1.225914  1.420297   1.420297  \n", "4761            -0.011823  0.658765  1.231804 -1.645991  -1.645991  \n", "4762            -0.011823 -1.517992  1.231804 -0.760174  -0.760174  \n", "\n", "[4763 rows x 22 columns]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[  7, 327],\n", "       [  7, 612]], dtype=int64)"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["conf"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initial class distribution:\n", " Heart Attack Risk\n", "0    5624\n", "1    3139\n", "Name: count, dtype: int64\n", "ADASYN failed, using SMOTE instead.\n", "Undersampling failed, using original data.\n", "\n", "=== Training with SMOTE ===\n", "\n", "Model: Random Forest\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.64      0.96      0.77      1125\n", "           1       0.39      0.05      0.09       628\n", "\n", "    accuracy                           0.63      1753\n", "   macro avg       0.52      0.50      0.43      1753\n", "weighted avg       0.55      0.63      0.53      1753\n", "\n", "ROC-AUC Score: 0.4958\n", "Precision-Recall AUC: 0.3577\n", "Best Threshold: 0.2100\n", "Adjusted Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       1.00      0.00      0.01      1125\n", "           1       0.36      1.00      0.53       628\n", "\n", "    accuracy                           0.36      1753\n", "   macro avg       0.68      0.50      0.27      1753\n", "weighted avg       0.77      0.36      0.20      1753\n", "\n", "\n", "Model: XGBoost\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.65      0.67      0.66      1125\n", "           1       0.38      0.36      0.37       628\n", "\n", "    accuracy                           0.56      1753\n", "   macro avg       0.52      0.52      0.52      1753\n", "weighted avg       0.55      0.56      0.56      1753\n", "\n", "ROC-AUC Score: 0.5146\n", "Precision-Recall AUC: 0.3717\n", "Best Threshold: 0.0572\n", "Adjusted Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.84      0.02      0.05      1125\n", "           1       0.36      0.99      0.53       628\n", "\n", "    accuracy                           0.37      1753\n", "   macro avg       0.60      0.51      0.29      1753\n", "weighted avg       0.67      0.37      0.22      1753\n", "\n", "\n", "=== Training with ADASYN ===\n", "\n", "Model: Random Forest\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.64      0.96      0.77      1125\n", "           1       0.39      0.05      0.09       628\n", "\n", "    accuracy                           0.63      1753\n", "   macro avg       0.52      0.50      0.43      1753\n", "weighted avg       0.55      0.63      0.53      1753\n", "\n", "ROC-AUC Score: 0.4958\n", "Precision-Recall AUC: 0.3577\n", "Best Threshold: 0.2100\n", "Adjusted Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       1.00      0.00      0.01      1125\n", "           1       0.36      1.00      0.53       628\n", "\n", "    accuracy                           0.36      1753\n", "   macro avg       0.68      0.50      0.27      1753\n", "weighted avg       0.77      0.36      0.20      1753\n", "\n", "\n", "Model: XGBoost\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.65      0.67      0.66      1125\n", "           1       0.38      0.36      0.37       628\n", "\n", "    accuracy                           0.56      1753\n", "   macro avg       0.52      0.52      0.52      1753\n", "weighted avg       0.55      0.56      0.56      1753\n", "\n", "ROC-AUC Score: 0.5146\n", "Precision-Recall AUC: 0.3717\n", "Best Threshold: 0.0572\n", "Adjusted Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.84      0.02      0.05      1125\n", "           1       0.36      0.99      0.53       628\n", "\n", "    accuracy                           0.37      1753\n", "   macro avg       0.60      0.51      0.29      1753\n", "weighted avg       0.67      0.37      0.22      1753\n", "\n", "\n", "=== Training with Hybrid (SMOTETomek) ===\n", "\n", "Model: Random Forest\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.65      0.88      0.75      1125\n", "           1       0.39      0.13      0.20       628\n", "\n", "    accuracy                           0.61      1753\n", "   macro avg       0.52      0.51      0.47      1753\n", "weighted avg       0.55      0.61      0.55      1753\n", "\n", "ROC-AUC Score: 0.4966\n", "Precision-Recall AUC: 0.3600\n", "Best Threshold: 0.2200\n", "Adjusted Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       1.00      0.01      0.01      1125\n", "           1       0.36      1.00      0.53       628\n", "\n", "    accuracy                           0.36      1753\n", "   macro avg       0.68      0.50      0.27      1753\n", "weighted avg       0.77      0.36      0.20      1753\n", "\n", "\n", "Model: XGBoost\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.65      0.65      0.65      1125\n", "           1       0.37      0.36      0.36       628\n", "\n", "    accuracy                           0.55      1753\n", "   macro avg       0.51      0.51      0.51      1753\n", "weighted avg       0.55      0.55      0.55      1753\n", "\n", "ROC-AUC Score: 0.5079\n", "Precision-Recall AUC: 0.3675\n", "Best Threshold: 0.0094\n", "Adjusted Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       1.00      0.00      0.00      1125\n", "           1       0.36      1.00      0.53       628\n", "\n", "    accuracy                           0.36      1753\n", "   macro avg       0.68      0.50      0.27      1753\n", "weighted avg       0.77      0.36      0.19      1753\n", "\n", "\n", "=== Training with Undersampling ===\n", "\n", "Model: Random Forest\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.64      0.99      0.78      1125\n", "           1       0.43      0.02      0.04       628\n", "\n", "    accuracy                           0.64      1753\n", "   macro avg       0.54      0.50      0.41      1753\n", "weighted avg       0.57      0.64      0.51      1753\n", "\n", "ROC-AUC Score: 0.5192\n", "Precision-Recall AUC: 0.3721\n", "Best Threshold: 0.1900\n", "Adjusted Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       1.00      0.00      0.00      1125\n", "           1       0.36      1.00      0.53       628\n", "\n", "    accuracy                           0.36      1753\n", "   macro avg       0.68      0.50      0.27      1753\n", "weighted avg       0.77      0.36      0.19      1753\n", "\n", "\n", "Model: XGBoost\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.64      0.68      0.66      1125\n", "           1       0.35      0.31      0.33       628\n", "\n", "    accuracy                           0.55      1753\n", "   macro avg       0.49      0.49      0.49      1753\n", "weighted avg       0.53      0.55      0.54      1753\n", "\n", "ROC-AUC Score: 0.5039\n", "Precision-Recall AUC: 0.3625\n", "Best Threshold: 0.0302\n", "Adjusted Classification Report:\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.89      0.01      0.01      1125\n", "           1       0.36      1.00      0.53       628\n", "\n", "    accuracy                           0.36      1753\n", "   macro avg       0.62      0.50      0.27      1753\n", "weighted avg       0.70      0.36      0.20      1753\n", "\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from imblearn.over_sampling import SMOTE, ADASYN\n", "from imblearn.under_sampling import RandomUnderSampler\n", "from imblearn.combine import SMOTETomek\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import classification_report, roc_auc_score, precision_recall_curve, auc\n", "import xgboost as xgb\n", "\n", "# Load dataset\n", "df = pd.read_csv(\"../datas/scaleddata.csv\")\n", "y = df.pop('Heart Attack Risk')\n", "\n", "# Check initial class distribution\n", "print(\"Initial class distribution:\\n\", y.value_counts())\n", "\n", "# Train-test split\n", "X_train, X_test, y_train, y_test = train_test_split(df, y, test_size=0.2, stratify=y, random_state=42)\n", "\n", "# Standardize features\n", "scaler = StandardScaler()\n", "X_train = scaler.fit_transform(X_train)\n", "X_test = scaler.transform(X_test)\n", "\n", "### Resampling Methods ###\n", "# SMOTE\n", "try:\n", "    smote = SMOTE(sampling_strategy=0.7, random_state=42)\n", "    X_train_smote, y_train_smote = smote.fit_resample(X_train, y_train)\n", "except ValueError:\n", "    print(\"SMOTE failed, falling back to original data.\")\n", "    X_train_smote, y_train_smote = X_train, y_train\n", "\n", "# ADASYN (with exception handling)\n", "try:\n", "    adasyn = ADASYN(sampling_strategy=0.7, random_state=42)\n", "    X_train_adasyn, y_train_adasyn = adasyn.fit_resample(X_train, y_train)\n", "except ValueError:\n", "    print(\"ADASYN failed, using SMOTE instead.\")\n", "    X_train_adasyn, y_train_adasyn = X_train_smote, y_train_smote\n", "\n", "# Hybrid Resampling (SMOTETomek)\n", "try:\n", "    smote_tomek = SMOTETomek(random_state=42)\n", "    X_train_hybrid, y_train_hybrid = smote_tomek.fit_resample(X_train, y_train)\n", "except ValueError:\n", "    print(\"SMOTETomek failed, using SMOTE instead.\")\n", "    X_train_hybrid, y_train_hybrid = X_train_smote, y_train_smote\n", "\n", "# Dynamic Undersampling Strategy\n", "minority_count = y_train.value_counts().min()\n", "majority_count = y_train.value_counts().max()\n", "valid_ratio = max(0.1, min(0.5, minority_count / majority_count))\n", "\n", "try:\n", "    under_sampler = RandomUnderSampler(sampling_strategy=valid_ratio, random_state=42)\n", "    X_train_under, y_train_under = under_sampler.fit_resample(X_train, y_train)\n", "except ValueError:\n", "    print(\"Undersampling failed, using original data.\")\n", "    X_train_under, y_train_under = X_train, y_train\n", "\n", "### Model Training & Evaluation ###\n", "models = {\n", "    \"Random Forest\": RandomForestClassifier(class_weight=\"balanced\", random_state=42),\n", "    \"XGBoost\": xgb.XGBClassifier(scale_pos_weight=(len(y_train) - sum(y_train)) / sum(y_train), random_state=42)\n", "}\n", "\n", "resampled_data = {\n", "    \"SMOTE\": (X_train_smote, y_train_smote),\n", "    \"ADASYN\": (X_train_adasyn, y_train_adasyn),\n", "    \"Hybrid (SMOTETomek)\": (X_train_hybrid, y_train_hybrid),\n", "    \"Undersampling\": (X_train_under, y_train_under)\n", "}\n", "\n", "for resampling_name, (X_resampled, y_resampled) in resampled_data.items():\n", "    print(f\"\\n=== Training with {resampling_name} ===\")\n", "    \n", "    for name, model in models.items():\n", "        print(f\"\\nModel: {name}\")\n", "        model.fit(X_resampled, y_resampled)\n", "        y_pred = model.predict(X_test)\n", "        y_pred_proba = model.predict_proba(X_test)[:, 1]\n", "\n", "        # Evaluation Metrics\n", "        print(classification_report(y_test, y_pred))\n", "        roc_auc = roc_auc_score(y_test, y_pred_proba)\n", "        print(f\"ROC-AUC Score: {roc_auc:.4f}\")\n", "        \n", "        precision, recall, thresholds = precision_recall_curve(y_test, y_pred_proba)\n", "        pr_auc = auc(recall, precision)\n", "        print(f\"Precision-Recall AUC: {pr_auc:.4f}\")\n", "        \n", "        # Best Threshold Selection\n", "        if thresholds.size > 0:\n", "            best_threshold = thresholds[np.argmax(precision * recall)]\n", "            print(f\"Best Threshold: {best_threshold:.4f}\")\n", "            y_pred_adj = (y_pred_proba >= best_threshold).astype(int)\n", "            print(\"Adjusted Classification Report:\\n\", classification_report(y_test, y_pred_adj))\n", "        else:\n", "            print(\"No valid threshold found.\")\n"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["scikit-learn version: 1.6.1\n", "imbalanced-learn version: 0.13.0\n"]}], "source": ["import sklearn\n", "import imblearn\n", "print(\"scikit-learn version:\", sklearn.__version__)\n", "print(\"imbalanced-learn version:\", imblearn.__version__)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}