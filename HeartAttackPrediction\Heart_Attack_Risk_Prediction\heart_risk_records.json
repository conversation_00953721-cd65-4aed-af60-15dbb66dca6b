[{"date": "2025-04-04 01:43", "risk_percentage": 28.0, "risk_class": "low", "key_factors": "High blood pressure, Elevated blood sugar, High heart rate", "recommendations": "Monitor blood pressure regularly; Control blood sugar levels; Regular cardiovascular check-ups", "age": "19", "gender": "0", "heart_rate": 160.0, "systolic": 160.0, "diastolic": 200.0, "blood_sugar": 500.0, "ckmb": 2.0, "troponin": 0.0}, {"date": "2025-04-04 01:45", "risk_percentage": 15.0, "risk_class": "low", "key_factors": "No significant risk factors", "recommendations": "Maintain healthy lifestyle", "age": "18", "gender": "1", "heart_rate": 100.0, "systolic": 120.0, "diastolic": 80.0, "blood_sugar": 100.0, "ckmb": 1.0, "troponin": 0.0}, {"date": "2025-04-04 01:46", "risk_percentage": 91.0, "risk_class": "high", "key_factors": "High Troponin", "recommendations": "Immediate medical attention recommended", "age": "18", "gender": "1", "heart_rate": 100.0, "systolic": 120.0, "diastolic": 80.0, "blood_sugar": 100.0, "ckmb": 1.0, "troponin": 1.0}, {"date": "2025-04-04 01:47", "risk_percentage": 12.0, "risk_class": "low", "key_factors": "No significant risk factors", "recommendations": "Maintain healthy lifestyle", "age": "18", "gender": "0", "heart_rate": 100.0, "systolic": 100.0, "diastolic": 80.0, "blood_sugar": 100.0, "ckmb": 1.0, "troponin": 0.0}, {"date": "2025-04-04 01:53", "risk_percentage": 12.0, "risk_class": "low", "key_factors": "No significant risk factors", "recommendations": "Maintain healthy lifestyle", "age": "18", "gender": "1", "heart_rate": 100.0, "systolic": 100.0, "diastolic": 80.0, "blood_sugar": 100.0, "ckmb": 1.0, "troponin": 0.0}, {"date": "2025-04-04 01:53", "risk_percentage": 90.0, "risk_class": "high", "key_factors": "High Troponin", "recommendations": "Immediate medical attention recommended", "age": "18", "gender": "1", "heart_rate": 100.0, "systolic": 100.0, "diastolic": 80.0, "blood_sugar": 100.0, "ckmb": 1.0, "troponin": 5.0}, {"date": "2025-04-04 11:38", "risk_percentage": 32.0, "risk_class": "medium", "key_factors": "High heart rate", "recommendations": "Regular cardiovascular check-ups", "age": "18", "gender": "1", "heart_rate": 120.0, "systolic": 45.0, "diastolic": 55.0, "blood_sugar": 25.0, "ckmb": 1.0, "troponin": 0.0}, {"date": "2025-04-04 11:40", "risk_percentage": 32.0, "risk_class": "medium", "key_factors": "High heart rate", "recommendations": "Regular cardiovascular check-ups", "age": "18", "gender": "1", "heart_rate": 120.0, "systolic": 45.0, "diastolic": 55.0, "blood_sugar": 25.0, "ckmb": 1.0, "troponin": 0.0}, {"date": "2025-04-04 11:40", "risk_percentage": 32.0, "risk_class": "medium", "key_factors": "High heart rate", "recommendations": "Regular cardiovascular check-ups", "age": "18", "gender": "1", "heart_rate": 136.0, "systolic": 45.0, "diastolic": 55.0, "blood_sugar": 25.0, "ckmb": 1.0, "troponin": 0.0}, {"date": "2025-04-04 12:08", "risk_percentage": 89.0, "risk_class": "high", "key_factors": "High blood pressure, High heart rate, High Troponin", "recommendations": "Monitor blood pressure regularly; Regular cardiovascular check-ups; Immediate medical attention recommended", "age": "18", "gender": "1", "heart_rate": 120.0, "systolic": 120.0, "diastolic": 100.0, "blood_sugar": 130.0, "ckmb": 3.0, "troponin": 5.0}]